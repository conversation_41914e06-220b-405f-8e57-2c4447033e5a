Rails.application.routes.draw do
  resources :pipeline_dashboard, only: [ :index, :show ]
  devise_for :users, controllers: {
    registrations: "users/registrations",
    sessions: "users/sessions"
  }

  root "root#index"
  get "landing", to: "landing#index"
  get "about", to: "about#index"

  # Projects and Landing Pages
  resources :projects, param: :slug do
    resources :landing_pages, param: :slug do
      member do
        get :preview
        get :edit
        patch :update
        post :publish
        delete :destroy
      end
      collection do
        get :new
        post :create
      end
    end
  end

  # Debug routes (remove in production)
  get "debug/session_info", to: "debug#session_info" unless Rails.env.production?
  get "debug/test_flash", to: "debug#test_flash" unless Rails.env.production?

  # Monitoring endpoints
  get "health", to: "monitoring#health"
  get "metrics", to: "monitoring#metrics"
  get "ready", to: "monitoring#ready"
  get "alive", to: "monitoring#alive"
  get "monitoring", to: "monitoring#dashboard"

  # Dashboard routes
  get "dashboard", to: "dashboard#index"
  get "dashboard/analytics", to: "dashboard#analytics"
  get "dashboard/reports", to: "dashboard#reports"

  # Business Templates
  resources :business_templates, only: [:index, :show] do
    member do
      post :apply
    end
  end

  # Report Builder
  resources :report_builder do
    member do
      get :preview
      post :duplicate
      post :add_component
      patch :update_component
      delete 'components/:component_id', to: 'report_builder#delete_component', as: :delete_component
      patch 'components/:component_id/move', to: 'report_builder#move_component', as: :move_component
      patch 'components/:component_id/resize', to: 'report_builder#resize_component', as: :resize_component
    end
    collection do
      get :gallery
    end
  end

  # Industry Templates
  resources :industry_templates, only: [:index, :show] do
    member do
      post :apply
    end
    collection do
      post :reset
    end
  end

  # Include data quality monitoring routes
  load Rails.root.join("config", "routes", "data_quality_routes.rb")

  # Include manual tasks routes
  load Rails.root.join("config", "routes", "manual_tasks_routes.rb")

  # Include API pipeline routes
  load Rails.root.join("config", "routes", "api_v1_pipeline_routes.rb")

  # ETL Pipeline Builder routes
  resources :etl_pipeline_builders do
    member do
      post :execute
      post :test
    end
    collection do
      get :available_extractors
      post :transformation_preview
      post :validate_pipeline
      get :export_pipeline
      post :import_pipeline
    end
  end

  # Pipeline Monitoring routes
  resources :pipeline_monitoring, only: [ :index, :show ] do
    member do
      get :live_updates
    end
    collection do
      get :system_health
      get :alerts
    end
  end

  # Alert Management routes (nested under pipeline monitoring)
  namespace :pipeline_monitoring do
    resources :alerts, only: [] do
      member do
        patch :acknowledge
        patch :resolve
        patch :dismiss
      end
    end
  end

  # Analytics - Legacy route for backward compatibility
  get "analytics", to: "analytics/dashboard#index"

  # New modular analytics routes
  namespace :analytics do
    root to: "dashboard#index"

    resource :dashboard, only: [ :show ], controller: "dashboard" do
      get :index, on: :collection, action: :index
    end

    resources :revenue, only: [ :index ] do
      collection do
        get :trends
        get :breakdown
      end
    end

    resources :customers, only: [ :index ] do
      collection do
        get :acquisition
        get :segments
        get :lifetime_value
      end
    end

    resources :products, only: [ :index ] do
      collection do
        get :performance
        get :inventory
        get :recommendations
      end
    end

    resources :risks, only: [ :index ] do
      collection do
        get :indicators
        get :opportunities
      end
    end
  end

  # AI-powered features
  namespace :ai do
    # Redirect old predictive_analytics path to new predictions path
    get 'predictive_analytics', to: redirect('/ai/predictions')
    
    resources :predictions, only: [:index] do
      collection do
        get :forecasts
        get :models
        get :scenarios
        post :run_prediction
        post :train_model
        post :configure_model
      end
    end

    resources :presentations do
      member do
        get :download
        get :status
      end
      collection do
        post :generate
        get :preview
      end
    end

    resources :queries, only: [ :index ] do
      collection do
        post :process_query
        get :suggestions
        post :validate
        get :examples
        post :export
      end
    end

    resources :real_time_analytics, only: [] do
      collection do
        get :dashboard
        get :live_data
        get :anomalies
        get :alerts
        get :insights
        get :predictions
        get :performance_metrics
        post :start_monitoring
        post :stop_monitoring
        post :configure_alerts
        post :dismiss_alert
        post :snooze_alert
        get :export_analytics
        get :health_check
      end
    end

    resources :bi_agent, only: [] do
      collection do
        get :dashboard
        post :start_agent
        post :stop_agent
        post :generate_insights
        post :weekly_report
        post :customer_analysis
        post :competitive_analysis
        post :scenario_planning
        get :agent_status
        post :configure_agent
        get :learning_status
        post :feedback
        get :export_insights
      end
    end

    # Natural Language Chat Interface
    resources :chat, only: [] do
      collection do
        post :create
        post :voice
        get :suggestions
        get :history
        post :feedback
        post :execute_action
      end
    end
    
    # Automated Actions
    resources :automated_actions, only: [:index, :show] do
      member do
        post :approve
        post :reject
        post :execute
        get :preview
      end
      collection do
        get :pending
        get :history
        post :configure
      end
    end

    resources :data_integration, only: [] do
      collection do
        get :dashboard
        get :dashboard_stats
        post :analyze_source
        post :generate_field_mapping
        post :optimize_data_source
        post :suggest_new_sources
        post :validate_integration_quality
        post :preview_integration
        get :integration_recommendations
        get :export_integration_plan
        post :optimize_all
        post :validate_quality
      end
    end

    resources :interactive_presentations, only: [] do
      collection do
        get :dashboard
        get :dashboard_stats
        post :create_presentation
        post :create_interactive
        post :create_live_dashboard
        post :create_data_story
        post :generate_content
        post :analyze_data
        post :suggest_visualizations
        get :presentation_templates
        get :export_presentation
        post :save_presentation
        post :share_presentation
        get :presentation_analytics
        post :duplicate_presentation
        delete :delete_presentation
      end

      member do
        get :show
        get :edit
        patch :update
        get :preview
        post :publish
        post :unpublish
        get :analytics
        post :clone
      end
    end
  end

  # Organization management
  resource :organization, only: [ :show, :edit, :update ] do
    member do
      get :billing
      get :usage_stats
      get :audit_logs
    end
  end

  # User management - constrain ID to numeric to avoid conflicts with Devise routes
  resources :users, constraints: { id: /\d+/ } do
    member do
      patch :change_role
      delete :remove_avatar
    end
  end

  # Delivery Preferences
  resources :delivery_preferences do
    member do
      patch :toggle
      get :preview
      post :test_delivery
    end
  end

  # Data sources
  resources :data_sources do
    collection do
      post :test_connection
      post :auto_save
      get :quality  # Add quality dashboard as collection route
      post :run_quality_check  # Add quality check endpoint
      get :download_sample_csv
      get :download_sample_excel
      get :download_sample_json
    end

    member do
      post :sync_now
      post :process_files
      get "preview_file/:file_id", action: :preview_file, as: :preview_file
      get "analyze_file/:file_id", action: :analyze_file, as: :analyze_file
      get "enhanced_preview/:file_id", action: :enhanced_preview, as: :enhanced_preview
      # Data quality routes
      get :quality, to: "data_quality#show"
      post :validate_quality, to: "data_quality#validate"
      get "quality/reports/:report_id", to: "data_quality#report", as: :quality_report
    end

    # Scheduled uploads
    resources :scheduled_uploads do
      member do
        patch :toggle_status
        post :execute_now
      end
    end

    # Logs for all scheduled uploads in a data source
    get "scheduled_uploads_logs", to: "scheduled_uploads#logs", as: :scheduled_uploads_logs
  end

  # API Routes
  namespace :api do
    namespace :v1 do
      # Authentication endpoints
      post "auth/login", to: "authentication#login"
      delete "auth/logout", to: "authentication#logout"
      post "auth/refresh", to: "authentication#refresh"
      get "auth/me", to: "authentication#me"

      # Organization endpoints
      resource :organization, only: [ :show, :update ] do
        get :usage_stats
        get :audit_logs
        get :billing_info
      end

      # Data Sources API
      resources :data_sources, except: [ :new, :edit ] do
        member do
          post :test_connection
          post :sync_now
          get :sync_status
          get :sync_history
          get :metrics
        end

        # Nested resources for data source configuration
        resources :extraction_jobs, only: [ :index, :show, :create, :destroy ] do
          member do
            post :retry
            post :cancel
          end
        end

        # Scheduled uploads API
        resources :scheduled_uploads, only: [ :index, :show, :create, :update, :destroy ] do
          member do
            patch :toggle_status
            post :execute_now
          end

          resources :upload_logs, only: [ :index, :show ]
        end
      end

      # Extraction Jobs API
      resources :extraction_jobs, only: [ :index, :show ] do
        member do
          post :retry
          post :cancel
          get :logs
        end
      end

      # Analytics and Reporting API
      namespace :analytics do
        get :dashboard_stats
        get :revenue_metrics
        get :customer_metrics
        get :product_metrics
        get :order_metrics
        get :trend_analysis

        # Time-series data endpoints
        get :revenue_over_time
        get :orders_over_time
        get :customers_over_time

        # Export endpoints
        post :export_report
        get "export_status/:job_id", action: :export_status
        get "download_export/:job_id", action: :download_export
      end

      # Visualizations API
      resources :visualizations, only: [ :index, :show, :create, :destroy ]

      # Notifications API
      resources :notifications do
        collection do
          get "unread_count"
          patch "mark_all_as_read"
        end
        member do
          patch "mark_as_read"
          patch "mark_as_unread"
        end
      end

      # Raw Data Access API
      resources :customers, only: [ :index, :show ] do
        collection do
          get :search
          get :segments
        end
      end

      resources :orders, only: [ :index, :show ] do
        collection do
          get :search
          get :by_status
          get :by_date_range
        end
      end

      resources :products, only: [ :index, :show ] do
        collection do
          get :search
          get :by_category
          get :low_stock
        end
      end

      # Real-time data endpoints (authenticated)
      namespace :realtime do
        get :metrics_stream
        get :job_status_stream
        get :notifications_stream
      end

      # Public demo endpoints (no authentication required)
      namespace :public do
        get :hero_stats
        get :demo_metrics
        get :metrics_stream
      end

      # Webhook endpoints for external integrations
      namespace :webhooks do
        post :shopify
        post :woocommerce
        post :stripe
        post :mailchimp
      end
    end
  end

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check
end
