<% content_for :title, "About Data Reflow - Democratizing Enterprise Analytics" %>

<div class="about-page" data-controller="landing-animation">

<!-- Hero Section -->
<section class="about-hero">
  <div class="container">
    <div class="about-hero__content">
      <h1 class="about-hero__title">About Data Reflow</h1>
      <p class="about-hero__subtitle">
        Democratizing enterprise-grade analytics for businesses of all sizes
      </p>
      <div class="about-hero__stats">
        <div class="about-hero__stat" 
             data-controller="counter" 
             data-counter-end-value="10000" 
             data-counter-suffix-value="+" 
             data-counter-separator-value="," 
             data-counter-duration-value="2500">
          <div class="about-hero__stat-number" data-counter-target="number">0+</div>
          <div class="about-hero__stat-label">Customers Worldwide</div>
        </div>
        <div class="about-hero__stat" 
             data-controller="counter" 
             data-counter-end-value="45" 
             data-counter-suffix-value="+" 
             data-counter-duration-value="2000">
          <div class="about-hero__stat-number" data-counter-target="number">0+</div>
          <div class="about-hero__stat-label">Countries</div>
        </div>
        <div class="about-hero__stat" 
             data-controller="counter" 
             data-counter-end-value="99.99" 
             data-counter-suffix-value="%" 
             data-counter-duration-value="2200">
          <div class="about-hero__stat-number" data-counter-target="number">0%</div>
          <div class="about-hero__stat-label">Uptime</div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Mission & Vision -->
<section class="mission-vision">
  <div class="container">
    <div class="mission-vision__grid">
      <div class="mission-vision__card">
        <div class="mission-vision__icon">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="mission-vision__title">Our Mission</h3>
        <p class="mission-vision__description"><%= @company_info[:mission] %></p>
      </div>
      <div class="mission-vision__card">
        <div class="mission-vision__icon">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
        <h3 class="mission-vision__title">Our Vision</h3>
        <p class="mission-vision__description"><%= @company_info[:vision] %></p>
      </div>
    </div>
  </div>
</section>

<!-- Company Story -->
<section class="company-story">
  <div class="container">
    <div class="company-story__content">
      <div class="company-story__text">
        <h2 class="company-story__title">Our Story</h2>
        <p class="company-story__description">
          Founded in <%= @company_info[:founded] %>, Data Reflow was born from the frustration of seeing small and medium enterprises 
          struggle with disconnected data systems while large corporations had access to sophisticated analytics platforms.
        </p>
        <p class="company-story__description">
          Our founders, having worked at companies like Google and Salesforce, witnessed firsthand how powerful data analytics 
          could transform businesses. They set out to create a platform that would bring enterprise-grade capabilities to 
          businesses of all sizes, without the complexity and cost barriers.
        </p>
        <p class="company-story__description">
          Today, we're proud to serve over <%= @stats[:customers] %> businesses across <%= @stats[:countries] %> countries, 
          helping them achieve an average ROI of <%= @stats[:avg_roi] %> within their first year.
        </p>
      </div>
      <div class="company-story__image">
        <%= image_tag "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=400&fit=crop",
            alt: "Data Reflow team collaboration",
            class: "company-story__img" %>
      </div>
    </div>
  </div>
</section>

<!-- Leadership Team -->
<section class="leadership-team">
  <div class="container">
    <div class="leadership-team__header">
      <h2 class="leadership-team__title">Leadership Team</h2>
      <p class="leadership-team__subtitle">
        Meet the experienced leaders driving our mission forward
      </p>
    </div>
    <div class="leadership-team__grid">
      <% @leadership_team.each do |member| %>
        <div class="leadership-team__member">
          <div class="leadership-team__member-image">
            <%= image_tag member[:image], 
                alt: member[:name],
                class: "leadership-team__member-img" %>
          </div>
          <div class="leadership-team__member-info">
            <h4 class="leadership-team__member-name"><%= member[:name] %></h4>
            <p class="leadership-team__member-role"><%= member[:role] %></p>
            <p class="leadership-team__member-bio"><%= member[:bio] %></p>
            <a href="<%= member[:linkedin] %>" class="leadership-team__member-linkedin">
              <svg fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Company Timeline -->
<section class="company-timeline">
  <div class="container">
    <div class="company-timeline__header">
      <h2 class="company-timeline__title">Our Journey</h2>
      <p class="company-timeline__subtitle">
        Key milestones in our mission to democratize data analytics
      </p>
    </div>
    <div class="company-timeline__content">
      <% @milestones.each_with_index do |milestone, index| %>
        <div class="company-timeline__item <%= 'company-timeline__item--reverse' if index.odd? %>">
          <div class="company-timeline__year"><%= milestone[:year] %></div>
          <div class="company-timeline__details">
            <h4 class="company-timeline__milestone-title"><%= milestone[:title] %></h4>
            <p class="company-timeline__milestone-description"><%= milestone[:description] %></p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Company Values -->
<section class="company-values">
  <div class="container">
    <div class="company-values__header">
      <h2 class="company-values__title">Our Values</h2>
      <p class="company-values__subtitle">
        The principles that guide everything we do
      </p>
    </div>
    <div class="company-values__grid">
      <% @values.each do |value| %>
        <div class="company-values__card">
          <div class="company-values__icon">
            <% case value[:icon] %>
            <% when "users" %>
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            <% when "brain" %>
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            <% when "target" %>
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            <% when "lightbulb" %>
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            <% end %>
          </div>
          <h4 class="company-values__card-title"><%= value[:title] %></h4>
          <p class="company-values__card-description"><%= value[:description] %></p>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- Awards & Recognition -->
<section class="awards-recognition">
  <div class="container">
    <div class="awards-recognition__header">
      <h2 class="awards-recognition__title">Awards & Recognition</h2>
      <p class="awards-recognition__subtitle">
        Industry recognition for our innovation and customer success
      </p>
    </div>
    <div class="awards-recognition__grid">
      <% @awards.each do |award| %>
        <div class="awards-recognition__card">
          <div class="awards-recognition__icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
          </div>
          <h4 class="awards-recognition__card-title"><%= award[:title] %></h4>
          <p class="awards-recognition__card-organization"><%= award[:organization] %></p>
          <p class="awards-recognition__card-year"><%= award[:year] %></p>
        </div>
      <% end %>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="about-cta">
  <div class="container">
    <div class="about-cta__content">
      <h2 class="about-cta__title">Ready to Transform Your Business?</h2>
      <p class="about-cta__description">
        Join thousands of businesses already using Data Reflow to make better decisions with their data.
      </p>
      <div class="about-cta__actions">
        <%= link_to new_user_registration_path, class: "btn btn--primary btn--lg" do %>
          Start Free Trial
        <% end %>
        <%= link_to root_path, class: "btn btn--outline btn--lg" do %>
          Learn More
        <% end %>
      </div>
      <p class="about-cta__note">
        14-day free trial • No credit card required • Setup in minutes
      </p>
    </div>
  </div>
</section>

</div>
