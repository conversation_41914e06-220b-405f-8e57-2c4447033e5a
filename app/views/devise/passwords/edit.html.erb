<% content_for :title, "Reset Your Password - Data Reflow" %>

<div class="auth-page">
  <!-- Premium Background -->
  <div class="auth-page__background"></div>

  <!-- Main Content -->
  <div class="auth-page__container">
    <div class="auth-page__password-reset">
      <div class="auth-page__form-card auth-page__form-card--centered">
        <!-- Success Icon -->
        <div class="auth-page__password-icon auth-page__password-icon--success">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
          </svg>
        </div>

        <!-- Header -->
        <div class="auth-page__form-header">
          <h2 class="auth-page__form-title">Create new password</h2>
          <p class="auth-page__form-subtitle">
            Your new password must be different from previous used passwords.
          </p>
        </div>

        <!-- Form -->
        <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put, class: "auth-page__form", data: { controller: "password-form" } }) do |f| %>
          <%= render "devise/shared/error_messages", resource: resource %>
          <%= f.hidden_field :reset_password_token %>

          <!-- New Password Field -->
          <div class="auth-page__field">
            <%= f.label :password, "New password", class: "auth-page__label" do %>
              New password
              <span class="auth-page__label-hint">(<%= @minimum_password_length || 6 %> characters minimum)</span>
            <% end %>
            <div class="auth-page__input-wrapper">
              <div class="auth-page__input-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <%= f.password_field :password,
                  autofocus: true,
                  autocomplete: "new-password",
                  class: "auth-page__input",
                  placeholder: "Enter your new password",
                  data: { password_form_target: "passwordInput" } %>
            </div>
            <!-- Password strength indicator -->
            <div class="auth-page__password-strength">
              <div class="auth-page__password-strength-bar" data-password-form-target="strengthBar"></div>
            </div>
            <p class="auth-page__field-help">Use a mix of letters, numbers, and symbols</p>
          </div>

          <!-- Confirm Password Field -->
          <div class="auth-page__field">
            <%= f.label :password_confirmation, "Confirm new password", class: "auth-page__label" %>
            <div class="auth-page__input-wrapper">
              <div class="auth-page__input-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <%= f.password_field :password_confirmation,
                  autocomplete: "new-password",
                  class: "auth-page__input",
                  placeholder: "Confirm your new password",
                  data: { password_form_target: "confirmationInput" } %>
            </div>
          </div>

          <!-- Password Requirements -->
          <div class="auth-page__password-requirements">
            <h4 class="auth-page__requirements-title">Password must contain:</h4>
            <ul class="auth-page__requirements-list">
              <li class="auth-page__requirement" data-requirement="length">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                At least <%= @minimum_password_length || 6 %> characters
              </li>
              <li class="auth-page__requirement" data-requirement="uppercase">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                One uppercase letter
              </li>
              <li class="auth-page__requirement" data-requirement="number-special">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                One number or special character
              </li>
            </ul>
          </div>

          <!-- Submit Button -->
          <div class="auth-page__submit">
            <%= f.submit "Reset password", class: "btn btn--primary btn--lg auth-page__submit-btn" %>
          </div>
        <% end %>

        <!-- Back to Sign In -->
        <div class="auth-page__back-link">
          <p class="auth-page__back-text">
            <%= link_to "Back to sign in", new_user_session_path,
                class: "auth-page__form-link" %>
          </p>
        </div>

        <!-- Security Badge -->
        <div class="auth-page__security">
          <div class="auth-page__security-badge">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Your password will be updated immediately
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
