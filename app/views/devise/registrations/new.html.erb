<% content_for :title, "Start Your Free Trial - Data Reflow" %>

<div class="auth-page">
  <!-- Premium Background -->
  <div class="auth-page__background"></div>


  <!-- Main Content -->
  <div class="auth-page__container">
    <div class="auth-page__content">

      <!-- Left Side - Value Proposition -->
      <div class="auth-page__branding">
        <!-- Logo -->
        <div class="auth-page__logo">
          <div class="auth-page__logo-icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div class="auth-page__logo-text">
            <h1 class="auth-page__brand-name">Data Reflow</h1>
            <p class="auth-page__brand-tagline">Enterprise Data Platform</p>
          </div>
        </div>

        <!-- Welcome Message -->
        <div class="auth-page__welcome">
          <h2 class="auth-page__welcome-title">
            Start transforming your
            <span class="auth-page__welcome-highlight">business data</span>
          </h2>
          <p class="auth-page__welcome-subtitle">
            Join thousands of companies using Data Reflow to make data-driven decisions with confidence.
          </p>
        </div>

        <!-- Benefits -->
        <div class="auth-page__features">
          <div class="auth-page__feature">
            <div class="auth-page__feature-icon auth-page__feature-icon--success">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <div class="auth-page__feature-content">
              <div class="auth-page__feature-title">14-day free trial</div>
              <div class="auth-page__feature-description">No credit card required. Full access to all features.</div>
            </div>
          </div>

          <div class="auth-page__feature">
            <div class="auth-page__feature-icon auth-page__feature-icon--primary">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div class="auth-page__feature-content">
              <div class="auth-page__feature-title">Quick setup</div>
              <div class="auth-page__feature-description">Connect your tools in minutes, see insights instantly.</div>
            </div>
          </div>

          <div class="auth-page__feature">
            <div class="auth-page__feature-icon auth-page__feature-icon--secondary">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
            <div class="auth-page__feature-content">
              <div class="auth-page__feature-title">24/7 Support</div>
              <div class="auth-page__feature-description">Get help whenever you need it from our expert team.</div>
            </div>
          </div>
        </div>

        <!-- Stats -->
        <div class="auth-page__stats">
          <div class="auth-page__stat">
            <div class="auth-page__stat-number">10K+</div>
            <div class="auth-page__stat-label">Companies</div>
          </div>
          <div class="auth-page__stat">
            <div class="auth-page__stat-number">340%</div>
            <div class="auth-page__stat-label">Avg ROI</div>
          </div>
          <div class="auth-page__stat">
            <div class="auth-page__stat-number">99.9%</div>
            <div class="auth-page__stat-label">Uptime</div>
          </div>
        </div>
      </div>

      <!-- Right Side - Sign Up Form -->
      <div class="auth-page__form-container">
        <div class="auth-page__form-card">
          <!-- Form Header -->
          <div class="auth-page__form-header">
            <h3 class="auth-page__form-title">Create your account</h3>
            <p class="auth-page__form-subtitle">
              Already have an account?
              <%= link_to "Sign in", new_user_session_path,
                  class: "auth-page__form-link" %>
            </p>
          </div>

          <!-- Form -->
          <%= form_for(resource, as: resource_name, url: registration_path(resource_name), local: true, html: { 
            class: "auth-page__form",
            data: { 
              controller: "registration-form",
              action: "submit->registration-form#handleSubmit"
            }
          }) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>
            
            <!-- Organization Name -->
            <div class="auth-page__field">
              <%= label_tag :organization_name, "Company name", class: "auth-page__label" %>
              <div class="auth-page__input-wrapper">
                <div class="auth-page__input-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
                <%= text_field_tag :organization_name, params[:organization_name],
                    class: "auth-page__input",
                    placeholder: "Acme Inc.",
                    data: { registration_form_target: "organizationInput" } %>
              </div>
            </div>

            <!-- Name Fields -->
            <div class="auth-page__field-group">
              <div class="auth-page__field">
                <%= f.label :first_name, "First name", class: "auth-page__label" %>
                <%= f.text_field :first_name,
                    class: "auth-page__input auth-page__input--no-icon",
                    placeholder: "John",
                    data: { registration_form_target: "firstNameInput" } %>
              </div>

              <div class="auth-page__field">
                <%= f.label :last_name, "Last name", class: "auth-page__label" %>
                <%= f.text_field :last_name,
                    class: "auth-page__input auth-page__input--no-icon",
                    placeholder: "Doe",
                    data: { registration_form_target: "lastNameInput" } %>
              </div>
            </div>

            <!-- Email Field -->
            <div class="auth-page__field">
              <%= f.label :email, "Work email", class: "auth-page__label" %>
              <div class="auth-page__input-wrapper">
                <div class="auth-page__input-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <%= f.email_field :email,
                    autofocus: true,
                    autocomplete: "email",
                    class: "auth-page__input",
                    placeholder: "<EMAIL>" %>
              </div>
            </div>

            <!-- Password Field -->
            <div class="auth-page__field">
              <%= f.label :password, class: "auth-page__label" do %>
                Password
                <span class="auth-page__label-hint">(<%= @minimum_password_length || 6 %> characters minimum)</span>
              <% end %>
              <div class="auth-page__input-wrapper">
                <div class="auth-page__input-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <%= f.password_field :password,
                    autocomplete: "new-password",
                    class: "auth-page__input",
                    placeholder: "Create a strong password",
                    data: { registration_form_target: "passwordInput" } %>
              </div>
              <!-- Password strength indicator -->
              <div class="auth-page__password-strength">
                <div class="auth-page__password-strength-bar" data-registration-form-target="passwordStrengthBar"></div>
              </div>
              <div class="auth-page__password-strength-text" data-registration-form-target="passwordStrength"></div>
            </div>

            <!-- Password Confirmation -->
            <div class="auth-page__field">
              <%= f.label :password_confirmation, "Confirm password", class: "auth-page__label" %>
              <div class="auth-page__input-wrapper">
                <div class="auth-page__input-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <%= f.password_field :password_confirmation,
                    autocomplete: "new-password",
                    class: "auth-page__input",
                    placeholder: "Confirm your password",
                    data: { registration_form_target: "passwordConfirmationInput" } %>
              </div>
            </div>

            <!-- Terms & Conditions -->
            <div class="auth-page__terms">
              <div class="auth-page__checkbox">
                <input type="checkbox" id="terms" required class="auth-page__checkbox-input">
                <label for="terms" class="auth-page__checkbox-label">
                  I agree to the
                  <%= link_to "Terms of Service", "#", class: "auth-page__form-link" %>
                  and
                  <%= link_to "Privacy Policy", "#", class: "auth-page__form-link" %>
                </label>
              </div>

              <div class="auth-page__checkbox">
                <input type="checkbox" id="marketing" class="auth-page__checkbox-input">
                <label for="marketing" class="auth-page__checkbox-label">
                  Send me tips and best practices to get the most out of Data Reflow
                </label>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="auth-page__submit">
              <%= f.submit "Create account", class: "btn btn--primary btn--lg auth-page__submit-btn" %>
            </div>
          <% end %>

          <!-- Features Summary -->
          <div class="auth-page__features-summary">
            <p class="auth-page__features-title">Your free trial includes:</p>
            <div class="auth-page__features-grid">
              <div class="auth-page__feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                All integrations
              </div>
              <div class="auth-page__feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Unlimited users
              </div>
              <div class="auth-page__feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Real-time analytics
              </div>
              <div class="auth-page__feature-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                24/7 support
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>