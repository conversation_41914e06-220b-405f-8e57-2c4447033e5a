<div class="dashboard-content">
  <!-- Executive Industry Templates <PERSON><PERSON> with Premium Glass Morphism -->
  <div class="executive-templates-header" style="
    background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
    backdrop-filter: blur(var(--blur-lg, 20px));
    -webkit-backdrop-filter: blur(var(--blur-lg, 20px));
    border: 1px solid rgba(var(--color-border-rgb), 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-32);
    margin-bottom: var(--space-32);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
  ">
    <!-- Premium Background Pattern -->
    <div style="
      position: absolute;
      top: 0;
      right: 0;
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.08) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(50%, -50%);
    "></div>

    <div style="
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 1;
    ">
      <div class="executive-title-section">
        <h1 style="
          font-size: var(--font-size-4xl);
          font-weight: var(--font-weight-bold);
          color: var(--color-text);
          margin: 0 0 var(--space-8) 0;
          line-height: var(--line-height-tight);
          letter-spacing: var(--letter-spacing-tight);
          display: flex;
          align-items: center;
          gap: var(--space-16);
        ">
          <div style="
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 12px 24px rgba(var(--color-primary-rgb), 0.3);
          ">
            <svg style="width: 32px; height: 32px; color: white;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
          Executive Industry Templates
        </h1>
        <p style="
          font-size: var(--font-size-lg);
          color: var(--color-text-secondary);
          margin: 0 0 var(--space-16) 0;
          font-weight: var(--font-weight-medium);
        ">Get started in minutes with pre-configured dashboards, integrations, and reports designed specifically for your business type</p>

        <!-- Enhanced Status Indicators -->
        <div style="display: flex; align-items: center; gap: var(--space-16);">
          <div style="
            display: flex;
            align-items: center;
            gap: var(--space-8);
            padding: var(--space-8) var(--space-16);
            background: rgba(var(--color-success-rgb), 0.1);
            border: 1px solid rgba(var(--color-success-rgb), 0.2);
            border-radius: var(--radius-full);
          ">
            <div style="
              width: 8px;
              height: 8px;
              background: var(--color-success);
              border-radius: 50%;
              animation: pulse 2s infinite;
            "></div>
            <span style="
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--color-success);
            ">Templates Ready</span>
          </div>
          <div style="
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
          "><%= @templates.count %> Industry Templates Available</div>
        </div>
      </div>

      <!-- Premium Executive Actions -->
      <div style="
        display: flex;
        align-items: center;
        gap: var(--space-12);
        position: relative;
        z-index: 1;
      ">
        <button class="btn btn--outline" style="
          background: rgba(var(--color-surface-rgb), 0.8);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          display: inline-flex;
          align-items: center;
          gap: var(--space-8);
        ">
          <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Help Center
        </button>
        <button class="btn btn--outline" style="
          background: rgba(var(--color-surface-rgb), 0.8);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          display: inline-flex;
          align-items: center;
          gap: var(--space-8);
        ">
          <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
          </svg>
          Filter
        </button>
      </div>
    </div>
  </div>

  <% if @applied_template.present? %>
    <!-- Executive Applied Template Notice -->
    <div style="
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-success-rgb), 0.05) 100%);
      border: 1px solid rgba(var(--color-success-rgb), 0.2);
      border-radius: var(--radius-xl);
      padding: var(--space-24);
      margin-bottom: var(--space-32);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      position: relative;
      overflow: hidden;
    ">
      <!-- Background Pattern -->
      <div style="
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(var(--color-success-rgb), 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(30%, -30%);
      "></div>

      <div style="
        display: flex;
        align-items: flex-start;
        gap: var(--space-20);
        position: relative;
        z-index: 1;
      ">
        <div style="
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
          border-radius: var(--radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 8px 16px rgba(var(--color-success-rgb), 0.3);
          flex-shrink: 0;
        ">
          <svg style="width: 24px; height: 24px; color: white;" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div style="flex: 1;">
          <h3 style="
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--color-success);
            margin: 0 0 var(--space-8) 0;
          ">
            <%= @applied_template.humanize %> Template Active
          </h3>
          <p style="
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            margin: 0 0 var(--space-16) 0;
            line-height: var(--line-height-relaxed);
          ">Your organization is successfully using the <%= @applied_template.humanize %> business template with all configured dashboards, reports, and integrations.</p>
          <div style="display: flex; align-items: center; gap: var(--space-12);">
            <%= link_to dashboard_path, class: "btn btn--primary btn--sm", style: "
              background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
              box-shadow: 0 4px 8px rgba(var(--color-success-rgb), 0.3);
              display: inline-flex;
              align-items: center;
              gap: var(--space-8);
            " do %>
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              Go to Dashboard
            <% end %>
            <%= link_to analytics_dashboard_index_path, class: "btn btn--outline btn--sm", style: "
              background: rgba(var(--color-surface-rgb), 0.8);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              display: inline-flex;
              align-items: center;
              gap: var(--space-8);
            " do %>
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              View Analytics
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Executive Templates Grid -->
  <div style="
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--space-24);
    margin-bottom: var(--space-48);
  ">
    <% @templates.each do |template| %>
      <div style="
        background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
        border: 1px solid rgba(var(--color-border-rgb), 0.2);
        border-radius: var(--radius-xl);
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        position: relative;
        <%= 'opacity: 0.75;' if @applied_template.present? %>
      " onmouseover="this.style.transform='translateY(-8px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0, 0, 0, 0.08)'">

        <!-- Premium Template Header -->
        <div style="
          background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
          padding: var(--space-32);
          color: white;
          position: relative;
          overflow: hidden;
        ">
          <!-- Header Background Pattern -->
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
          "></div>

          <div style="position: relative; z-index: 1;">
            <div style="
              font-size: 3.5rem;
              margin-bottom: var(--space-16);
              filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
            "><%= template[:icon] %></div>
            <h2 style="
              font-size: var(--font-size-2xl);
              font-weight: var(--font-weight-bold);
              margin: 0 0 var(--space-12) 0;
              color: white;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            "><%= template[:name] %></h2>
            <p style="
              color: rgba(255, 255, 255, 0.9);
              line-height: var(--line-height-relaxed);
              font-size: var(--font-size-sm);
              margin: 0;
            "><%= template[:description] %></p>
          </div>
        </div>

        <!-- Features -->
        <div style="padding: var(--space-24);">
          <h3 style="font-size: var(--font-size-sm); font-weight: var(--font-weight-semibold); color: var(--color-text-secondary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--space-12);">
            Key Features
          </h3>
          <ul style="margin-bottom: var(--space-24);">
            <% template[:features].first(4).each do |feature| %>
              <li style="display: flex; align-items: flex-start; margin-bottom: var(--space-8);">
                <svg style="width: 20px; height: 20px; color: var(--color-success); margin-top: 2px; margin-right: var(--space-8); flex-shrink: 0;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span style="font-size: var(--font-size-sm); color: var(--color-text-secondary);"><%= feature %></span>
              </li>
            <% end %>
          </ul>

          <!-- Premium Integrations Section -->
          <h3 style="
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin: 0 0 var(--space-16) 0;
          ">Integrations</h3>
          <div style="
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-8);
            margin-bottom: var(--space-24);
          ">
            <% template[:data_sources].each do |source| %>
              <span style="
                display: inline-flex;
                align-items: center;
                padding: var(--space-6) var(--space-12);
                border-radius: var(--radius-full);
                font-size: var(--font-size-xs);
                font-weight: var(--font-weight-medium);
                background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
                border: 1px solid rgba(var(--color-primary-rgb), 0.2);
                color: var(--color-primary);
                backdrop-filter: blur(10px);
              "><%= source %></span>
            <% end %>
          </div>

          <!-- Premium Action Buttons -->
          <div style="display: flex; flex-direction: column; gap: var(--space-12);">
            <%= link_to business_template_path(template[:id]), class: "btn btn--outline", style: "
              background: rgba(var(--color-surface-rgb), 0.8);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              width: 100%;
              justify-content: center;
              display: flex;
              align-items: center;
              gap: var(--space-8);
            " do %>
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
              View Details
            <% end %>

            <% if @applied_template.blank? && (current_user.organization_admin? || current_user.organization_owner?) %>
              <%= button_to apply_business_template_path(template[:id]),
                  method: :post,
                  params: { include_sample_data: true },
                  data: {
                    turbo_confirm: "This will set up #{template[:name]} template with all integrations and dashboards. Continue?"
                  },
                  class: "btn btn--primary",
                  style: "
                    background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
                    box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
                    width: 100%;
                    justify-content: center;
                    display: flex;
                    align-items: center;
                    gap: var(--space-8);
                  " do %>
                <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                Apply Template
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Benefits Section -->
  <div class="ai-insights-panel" style="margin-top: var(--space-48);">
    <h2 style="font-size: var(--font-size-2xl); font-weight: var(--font-weight-bold); color: var(--color-text); text-align: center; margin-bottom: var(--space-32);">
      Why Use Industry Templates?
    </h2>

    <div class="metrics-grid" style="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--space-32);">
      <div style="text-align: center;">
        <div style="display: inline-flex; align-items: center; justify-content: center; width: 64px; height: 64px; background: var(--color-primary-light); border-radius: var(--radius-full); margin-bottom: var(--space-16);">
          <svg style="width: 32px; height: 32px; color: var(--color-primary);" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h3 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--color-text); margin-bottom: var(--space-8);">Instant Setup</h3>
        <p style="color: var(--color-text-secondary); line-height: 1.6;">Pre-configured dashboards and reports ready in minutes, not weeks</p>
      </div>

      <div style="text-align: center;">
        <div style="display: inline-flex; align-items: center; justify-content: center; width: 64px; height: 64px; background: var(--color-primary-light); border-radius: var(--radius-full); margin-bottom: var(--space-16);">
          <svg style="width: 32px; height: 32px; color: var(--color-primary);" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--color-text); margin-bottom: var(--space-8);">Industry Best Practices</h3>
        <p style="color: var(--color-text-secondary); line-height: 1.6;">Metrics and KPIs that matter most for your specific business type</p>
      </div>

      <div style="text-align: center;">
        <div style="display: inline-flex; align-items: center; justify-content: center; width: 64px; height: 64px; background: var(--color-primary-light); border-radius: var(--radius-full); margin-bottom: var(--space-16);">
          <svg style="width: 32px; height: 32px; color: var(--color-primary);" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--color-text); margin-bottom: var(--space-8);">Proven ROI</h3>
        <p style="color: var(--color-text-secondary); line-height: 1.6;">Start seeing insights that drive revenue from day one</p>
      </div>
    </div>
  </div>

  <!-- Executive CTA Section -->
  <% if @applied_template.blank? %>
    <div style="
      margin-top: var(--space-48);
      text-align: center;
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-info-rgb), 0.02) 100%);
      border: 1px solid rgba(var(--color-border-rgb), 0.2);
      border-radius: var(--radius-xl);
      padding: var(--space-32);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    ">
      <p style="
        color: var(--color-text-secondary);
        margin: 0 0 var(--space-24) 0;
        font-size: var(--font-size-lg);
        line-height: var(--line-height-relaxed);
      ">
        Not sure which template to choose? Our team can help you select the perfect fit.
      </p>
      <div style="display: flex; align-items: center; justify-content: center; gap: var(--space-16);">
        <%= link_to "#", class: "btn btn--primary", style: "
          background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
          box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
          display: inline-flex;
          align-items: center;
          gap: var(--space-8);
        " do %>
          <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
          </svg>
          Contact Support
        <% end %>
        <%= link_to "#", class: "btn btn--outline", style: "
          background: rgba(var(--color-surface-rgb), 0.8);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          display: inline-flex;
          align-items: center;
          gap: var(--space-8);
        " do %>
          <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Learn More
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<!-- Premium CSS Animations and Dark Mode Enhancements -->
<style>
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Enhanced hover effects for template cards */
  .executive-templates-header [onmouseover] {
    cursor: pointer;
  }

  /* Dark mode enhancements for glass morphism */
  @media (prefers-color-scheme: dark), [data-color-scheme="dark"] {
    /* Enhanced glass morphism for dark mode */
    .executive-templates-header,
    .ai-insights-panel {
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.9) 0%, rgba(var(--color-primary-rgb), 0.08) 100%) !important;
      border: 1px solid rgba(var(--color-border-rgb), 0.4) !important;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    }

    /* Enhanced template cards for dark mode */
    .executive-templates-header + div > div {
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.8) 0%, rgba(var(--color-primary-rgb), 0.05) 100%) !important;
      border: 1px solid rgba(var(--color-border-rgb), 0.4) !important;
    }

    /* Enhanced text contrast for dark mode */
    .executive-templates-header h1,
    .ai-insights-panel h2 {
      color: var(--color-text) !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Enhanced backdrop blur for dark mode */
    .executive-templates-header,
    .ai-insights-panel {
      backdrop-filter: blur(24px) !important;
      -webkit-backdrop-filter: blur(24px) !important;
    }

    /* Enhanced status indicators for dark mode */
    .executive-templates-header [style*="pulse"] {
      box-shadow: 0 0 8px rgba(var(--color-success-rgb), 0.6) !important;
    }
  }

  /* Responsive design for template sections */
  @media (max-width: 768px) {
    .executive-templates-header > div {
      flex-direction: column !important;
      gap: var(--space-24) !important;
    }

    .executive-templates-header .executive-title-section h1 {
      font-size: var(--font-size-2xl) !important;
    }

    .executive-templates-header + div {
      grid-template-columns: 1fr !important;
    }

    .metrics-grid {
      grid-template-columns: 1fr !important;
    }
  }
</style>