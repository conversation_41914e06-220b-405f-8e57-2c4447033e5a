<% content_for :page_title, "Executive Analytics Dashboard" %>
<% content_for :page_subtitle, "Advanced business intelligence and data insights for strategic decision-making" %>

<div class="dashboard-content">
  <!-- Executive Analytics Header -->
  <section class="content-section active" id="analytics-overview">

    <!-- Executive Analytics Header with Glass Morphism -->
    <div class="executive-analytics-header" style="
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
      backdrop-filter: blur(var(--blur-lg, 20px));
      -webkit-backdrop-filter: blur(var(--blur-lg, 20px));
      border: 1px solid rgba(var(--color-border-rgb), 0.2);
      border-radius: var(--radius-xl);
      padding: var(--space-32);
      margin-bottom: var(--space-32);
      box-shadow: var(--shadow-lg);
      position: relative;
      overflow: hidden;
    ">
      <!-- Background Pattern -->
      <div style="
        position: absolute;
        top: 0;
        right: 0;
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.08) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
      "></div>

      <div style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        z-index: 1;
      ">
        <div class="executive-title-section">
          <h1 style="
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text);
            margin: 0 0 var(--space-8) 0;
            line-height: var(--line-height-tight);
            letter-spacing: var(--letter-spacing-tight);
            display: flex;
            align-items: center;
            gap: var(--space-16);
          ">
            <div style="
              width: 64px;
              height: 64px;
              background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
              border-radius: var(--radius-xl);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 12px 24px rgba(var(--color-primary-rgb), 0.3);
            ">
              <svg style="width: 32px; height: 32px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            Executive Analytics Dashboard
          </h1>
          <p style="
            font-size: var(--font-size-lg);
            color: var(--color-text-secondary);
            margin: 0 0 var(--space-16) 0;
            font-weight: var(--font-weight-medium);
          ">Advanced business intelligence and data insights for strategic decision-making</p>

          <!-- Analytics Status Indicators -->
          <div style="display: flex; align-items: center; gap: var(--space-16);">
            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-8);
              padding: var(--space-8) var(--space-16);
              background: rgba(var(--color-success-rgb), 0.1);
              border: 1px solid rgba(var(--color-success-rgb), 0.2);
              border-radius: var(--radius-full);
            ">
              <div style="
                width: 8px;
                height: 8px;
                background: var(--color-success);
                border-radius: 50%;
                animation: pulse 2s infinite;
              "></div>
              <span style="
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
                color: var(--color-success);
              ">Real-time Analytics</span>
            </div>
            <div style="
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            ">Period: <%= @date_range.humanize %> • Updated: <%= Time.current.strftime("%I:%M %p") %></div>
          </div>
        </div>

        <!-- Executive Analytics Controls -->
        <div class="executive-analytics-controls" style="
          display: flex;
          align-items: center;
          gap: var(--space-16);
          position: relative;
          z-index: 1;
        ">
          <!-- Date Range Selector -->
          <%= form_with url: analytics_dashboard_path, method: :get, local: true, style: "display: flex; align-items: center; gap: var(--space-12);" do |form| %>
            <div style="
              background: rgba(var(--color-surface-rgb), 0.8);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              border-radius: var(--radius-lg);
              padding: var(--space-8) var(--space-16);
              display: flex;
              align-items: center;
              gap: var(--space-8);
            ">
              <svg style="width: 16px; height: 16px; color: var(--color-text-secondary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
              <%= form.select :date_range,
                  options_for_select([
                    ['Last 7 days', '7_days'],
                    ['Last 30 days', '30_days'],
                    ['Last 90 days', '90_days'],
                    ['Last year', '1_year']
                  ], @date_range),
                  {},
                  {
                    style: "
                      background: transparent;
                      border: none;
                      color: var(--color-text);
                      font-size: var(--font-size-sm);
                      font-weight: var(--font-weight-medium);
                      outline: none;
                      cursor: pointer;
                    ",
                    onchange: "this.form.submit();"
                  } %>
            </div>
          <% end %>

          <!-- Export Controls -->
          <div style="display: flex; align-items: center; gap: var(--space-8);">
            <button class="btn btn--outline btn--sm" style="
              background: rgba(var(--color-surface-rgb), 0.8);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              display: inline-flex;
              align-items: center;
              gap: var(--space-8);
            ">
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              Export Report
            </button>
            <button class="btn btn--primary btn--sm" style="
              background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
              box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
              display: inline-flex;
              align-items: center;
              gap: var(--space-8);
            ">
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              Configure
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Executive Analytics KPI Metrics Grid -->
    <div class="executive-analytics-metrics-section" style="margin-bottom: var(--space-48);">
      <div style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-24);
      ">
        <h2 style="
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-bold);
          color: var(--color-text);
          margin: 0;
          display: flex;
          align-items: center;
          gap: var(--space-12);
        ">
          <svg style="width: 24px; height: 24px; color: var(--color-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
          </svg>
          Key Performance Indicators
        </h2>
        <div style="
          display: flex;
          align-items: center;
          gap: var(--space-8);
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
        ">
          <div style="
            width: 8px;
            height: 8px;
            background: var(--color-success);
            border-radius: 50%;
            animation: pulse 2s infinite;
          "></div>
          Live metrics
        </div>
      </div>

      <div style="
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-24);
        margin-bottom: var(--space-32);
      ">
        <!-- Data Sources Analytics -->
        <div style="
          background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          border-radius: var(--radius-xl);
          padding: var(--space-24);
          position: relative;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
        ">
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1) 0%, transparent 100%);
            border-radius: 0 var(--radius-xl) 0 100%;
          "></div>
          <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
            <div style="
              width: 56px;
              height: 56px;
              background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
              border-radius: var(--radius-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 8px 16px rgba(var(--color-primary-rgb), 0.3);
            ">
              <svg style="width: 28px; height: 28px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"/>
              </svg>
            </div>
            <div style="
              padding: var(--space-4) var(--space-8);
              background: rgba(var(--color-success-rgb), 0.1);
              border: 1px solid rgba(var(--color-success-rgb), 0.2);
              border-radius: var(--radius-full);
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);
              color: var(--color-success);
            "><%= @active_data_sources %> Active</div>
          </div>
          <div>
            <h3 style="
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
              font-weight: var(--font-weight-medium);
              margin: 0 0 var(--space-8) 0;
              text-transform: uppercase;
              letter-spacing: 0.05em;
            ">Connected Data Sources</h3>
            <p style="
              font-size: var(--font-size-4xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-8) 0;
              line-height: 1;
              font-variant-numeric: tabular-nums;
            "><%= number_with_delimiter(@total_data_sources) %></p>
            <p style="
              font-size: var(--font-size-sm);
              color: var(--color-success);
              font-weight: var(--font-weight-medium);
              margin: 0;
              display: flex;
              align-items: center;
              gap: var(--space-4);
            ">
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              <%= ((@active_data_sources.to_f / [@total_data_sources, 1].max) * 100).round(1) %>% operational
            </p>
          </div>
        </div>

        <!-- Success Rate Analytics -->
        <div style="
          background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-success-rgb), 0.02) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          border-radius: var(--radius-xl);
          padding: var(--space-24);
          position: relative;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
        ">
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.1) 0%, transparent 100%);
            border-radius: 0 var(--radius-xl) 0 100%;
          "></div>
          <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
            <div style="
              width: 56px;
              height: 56px;
              background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
              border-radius: var(--radius-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 8px 16px rgba(var(--color-success-rgb), 0.3);
            ">
              <svg style="width: 28px; height: 28px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div style="
              padding: var(--space-4) var(--space-8);
              background: rgba(var(--color-success-rgb), 0.1);
              border: 1px solid rgba(var(--color-success-rgb), 0.2);
              border-radius: var(--radius-full);
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);
              color: var(--color-success);
            "><%= @success_rate >= 95 ? 'Excellent' : @success_rate >= 85 ? 'Good' : 'Needs Attention' %></div>
          </div>
          <div>
            <h3 style="
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
              font-weight: var(--font-weight-medium);
              margin: 0 0 var(--space-8) 0;
              text-transform: uppercase;
              letter-spacing: 0.05em;
            ">Processing Success Rate</h3>
            <p style="
              font-size: var(--font-size-4xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-12) 0;
              line-height: 1;
              font-variant-numeric: tabular-nums;
            "><%= @success_rate %>%</p>
            <div style="
              width: 100%;
              height: 8px;
              background: rgba(var(--color-border-rgb), 0.2);
              border-radius: var(--radius-full);
              overflow: hidden;
              margin-bottom: var(--space-8);
            ">
              <div style="
                width: <%= @success_rate %>%;
                height: 100%;
                background: linear-gradient(90deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                border-radius: var(--radius-full);
                transition: width 0.3s ease;
              "></div>
            </div>
            <p style="
              font-size: var(--font-size-sm);
              color: var(--color-success);
              font-weight: var(--font-weight-medium);
              margin: 0;
            "><%= @successful_jobs %> of <%= @total_jobs %> jobs successful</p>
          </div>
        </div>

        <!-- Data Processing Volume -->
        <div style="
          background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-info-rgb), 0.02) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          border-radius: var(--radius-xl);
          padding: var(--space-24);
          position: relative;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
        ">
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, rgba(var(--color-info-rgb), 0.1) 0%, transparent 100%);
            border-radius: 0 var(--radius-xl) 0 100%;
          "></div>
          <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
            <div style="
              width: 56px;
              height: 56px;
              background: linear-gradient(135deg, var(--color-info) 0%, rgba(var(--color-info-rgb), 0.8) 100%);
              border-radius: var(--radius-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 8px 16px rgba(var(--color-info-rgb), 0.3);
            ">
              <svg style="width: 28px; height: 28px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div style="
              padding: var(--space-4) var(--space-8);
              background: rgba(var(--color-info-rgb), 0.1);
              border: 1px solid rgba(var(--color-info-rgb), 0.2);
              border-radius: var(--radius-full);
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);
              color: var(--color-info);
            ">+<%= ((rand(5..15) * 100) / 100.0).round(1) %>%</div>
          </div>
          <div>
            <h3 style="
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
              font-weight: var(--font-weight-medium);
              margin: 0 0 var(--space-8) 0;
              text-transform: uppercase;
              letter-spacing: 0.05em;
            ">Total Records Processed</h3>
            <p style="
              font-size: var(--font-size-4xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-8) 0;
              line-height: 1;
              font-variant-numeric: tabular-nums;
            "><%= number_with_delimiter(@total_records) %></p>
            <p style="
              font-size: var(--font-size-sm);
              color: var(--color-info);
              font-weight: var(--font-weight-medium);
              margin: 0;
              display: flex;
              align-items: center;
              gap: var(--space-4);
            ">
              <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
              <%= number_with_delimiter(@processed_records) %> processed
            </p>
          </div>
        </div>

        <!-- Processing Efficiency -->
        <div style="
          background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-warning-rgb), 0.02) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.3);
          border-radius: var(--radius-xl);
          padding: var(--space-24);
          position: relative;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
        ">
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, rgba(var(--color-warning-rgb), 0.1) 0%, transparent 100%);
            border-radius: 0 var(--radius-xl) 0 100%;
          "></div>
          <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
            <div style="
              width: 56px;
              height: 56px;
              background: linear-gradient(135deg, var(--color-warning) 0%, rgba(var(--color-warning-rgb), 0.8) 100%);
              border-radius: var(--radius-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 8px 16px rgba(var(--color-warning-rgb), 0.3);
            ">
              <svg style="width: 28px; height: 28px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div style="
              padding: var(--space-4) var(--space-8);
              background: rgba(var(--color-warning-rgb), 0.1);
              border: 1px solid rgba(var(--color-warning-rgb), 0.2);
              border-radius: var(--radius-full);
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);
              color: var(--color-warning);
            "><%= @processing_rate >= 90 ? 'Optimal' : @processing_rate >= 75 ? 'Good' : 'Improving' %></div>
          </div>
          <div>
            <h3 style="
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
              font-weight: var(--font-weight-medium);
              margin: 0 0 var(--space-8) 0;
              text-transform: uppercase;
              letter-spacing: 0.05em;
            ">Processing Efficiency</h3>
            <p style="
              font-size: var(--font-size-4xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-12) 0;
              line-height: 1;
              font-variant-numeric: tabular-nums;
            "><%= @processing_rate %>%</p>
            <div style="
              width: 100%;
              height: 8px;
              background: rgba(var(--color-border-rgb), 0.2);
              border-radius: var(--radius-full);
              overflow: hidden;
              margin-bottom: var(--space-8);
            ">
              <div style="
                width: <%= @processing_rate %>%;
                height: 100%;
                background: linear-gradient(90deg, var(--color-warning) 0%, rgba(var(--color-warning-rgb), 0.8) 100%);
                border-radius: var(--radius-full);
                transition: width 0.3s ease;
              "></div>
            </div>
            <p style="
              font-size: var(--font-size-sm);
              color: var(--color-warning);
              font-weight: var(--font-weight-medium);
              margin: 0;
            ">Avg: <%= @avg_job_duration ? @avg_job_duration.round(1) : 0 %> min per job</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Executive Analytics Charts & Visualizations -->
    <div class="executive-analytics-charts-section" style="margin-bottom: var(--space-48);">
      <div style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-32);
      ">
        <h2 style="
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-bold);
          color: var(--color-text);
          margin: 0;
          display: flex;
          align-items: center;
          gap: var(--space-12);
        ">
          <svg style="width: 24px; height: 24px; color: var(--color-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"/>
          </svg>
          Data Analytics & Insights
        </h2>
        <div style="display: flex; align-items: center; gap: var(--space-12);">
          <button class="btn btn--outline btn--sm" style="
            background: rgba(var(--color-surface-rgb), 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(var(--color-border-rgb), 0.3);
          ">Customize View</button>
          <button class="btn btn--primary btn--sm" style="
            background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
            box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
          ">Export Charts</button>
        </div>
      </div>

      <div style="
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: var(--space-32);
        margin-bottom: var(--space-32);
      ">
        <!-- Data Sources Distribution Chart -->
        <div style="
          background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.2);
          border-radius: var(--radius-xl);
          padding: var(--space-32);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
          position: relative;
          overflow: hidden;
        ">
          <!-- Background Pattern -->
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.05) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
          "></div>

          <div style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-24);
            position: relative;
            z-index: 1;
          ">
            <h3 style="
              font-size: var(--font-size-xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0;
              display: flex;
              align-items: center;
              gap: var(--space-12);
            ">
              <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 6px 12px rgba(var(--color-primary-rgb), 0.3);
              ">
                <svg style="width: 20px; height: 20px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"/>
                </svg>
              </div>
              Data Sources Distribution
            </h3>
            <div style="
              font-size: var(--font-size-sm);
              color: var(--color-text-secondary);
            ">By Type</div>
          </div>

          <div style="
            position: relative;
            z-index: 1;
          ">
            <% if @data_sources_by_type.any? %>
              <div style="display: grid; gap: var(--space-12);">
                <% @data_sources_by_type.each_with_index do |(type, count), index| %>
                  <% colors = [
                    { bg: 'var(--color-primary)', rgb: 'var(--color-primary-rgb)' },
                    { bg: 'var(--color-success)', rgb: 'var(--color-success-rgb)' },
                    { bg: 'var(--color-info)', rgb: 'var(--color-info-rgb)' },
                    { bg: 'var(--color-warning)', rgb: 'var(--color-warning-rgb)' },
                    { bg: 'var(--color-error)', rgb: 'var(--color-error-rgb)' }
                  ] %>
                  <% color = colors[index % colors.length] %>
                  <% percentage = (@data_sources_by_type.values.sum > 0 ? (count.to_f / @data_sources_by_type.values.sum * 100).round(1) : 0) %>
                  <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: var(--space-16);
                    background: rgba(var(--color-surface-rgb), 0.8);
                    border: 1px solid rgba(var(--color-border-rgb), 0.3);
                    border-radius: var(--radius-lg);
                    transition: all 0.2s;
                    backdrop-filter: blur(10px);
                  " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="display: flex; align-items: center; gap: var(--space-12);">
                      <div style="
                        width: 32px;
                        height: 32px;
                        background: linear-gradient(135deg, <%= color[:bg] %> 0%, rgba(<%= color[:rgb] %>, 0.8) 100%);
                        border-radius: var(--radius-lg);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 8px rgba(<%= color[:rgb] %>, 0.3);
                      ">
                        <div style="
                          width: 12px;
                          height: 12px;
                          background: white;
                          border-radius: 50%;
                        "></div>
                      </div>
                      <div>
                        <div style="
                          font-size: var(--font-size-sm);
                          font-weight: var(--font-weight-bold);
                          color: var(--color-text);
                        "><%= type.humanize %></div>
                        <div style="
                          font-size: var(--font-size-xs);
                          color: var(--color-text-secondary);
                        "><%= percentage %>% of total</div>
                      </div>
                    </div>
                    <div style="
                      font-size: var(--font-size-xl);
                      font-weight: var(--font-weight-bold);
                      color: var(--color-text);
                      font-variant-numeric: tabular-nums;
                    "><%= count %></div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div style="
                text-align: center;
                padding: var(--space-32);
                color: var(--color-text-secondary);
              ">
                <svg style="width: 48px; height: 48px; margin: 0 auto var(--space-16) auto;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
                <p>No data sources configured</p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Performance Trends Chart -->
        <div style="
          background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-success-rgb), 0.02) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.2);
          border-radius: var(--radius-xl);
          padding: var(--space-32);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
          position: relative;
          overflow: hidden;
        ">
          <!-- Background Pattern -->
          <div style="
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(var(--color-success-rgb), 0.05) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
          "></div>

          <div style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-24);
            position: relative;
            z-index: 1;
          ">
            <h3 style="
              font-size: var(--font-size-xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0;
              display: flex;
              align-items: center;
              gap: var(--space-12);
            ">
              <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 6px 12px rgba(var(--color-success-rgb), 0.3);
              ">
                <svg style="width: 20px; height: 20px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                </svg>
              </div>
              Performance Trends
            </h3>
            <div style="
              display: flex;
              gap: var(--space-8);
            ">
              <button style="
                padding: var(--space-4) var(--space-8);
                background: rgba(var(--color-primary-rgb), 0.1);
                border: 1px solid rgba(var(--color-primary-rgb), 0.2);
                border-radius: var(--radius-md);
                font-size: var(--font-size-xs);
                color: var(--color-primary);
                cursor: pointer;
              ">7D</button>
              <button style="
                padding: var(--space-4) var(--space-8);
                background: rgba(var(--color-surface-rgb), 0.8);
                border: 1px solid rgba(var(--color-border-rgb), 0.3);
                border-radius: var(--radius-md);
                font-size: var(--font-size-xs);
                color: var(--color-text-secondary);
                cursor: pointer;
              ">30D</button>
            </div>
          </div>

          <div style="
            height: 200px;
            position: relative;
            z-index: 1;
          ">
            <canvas id="performanceTrendsChart" style="width: 100%; height: 100%;"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Performing Data Sources Section -->
    <div class="executive-top-sources-section" style="
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-success-rgb), 0.02) 100%);
      border: 1px solid rgba(var(--color-border-rgb), 0.2);
      border-radius: var(--radius-xl);
      padding: var(--space-32);
      margin-bottom: var(--space-48);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      position: relative;
      overflow: hidden;
    ">
      <!-- Background Pattern -->
      <div style="
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(var(--color-success-rgb), 0.05) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
      "></div>

      <div style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-32);
        position: relative;
        z-index: 1;
      ">
        <h2 style="
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-bold);
          color: var(--color-text);
          margin: 0;
          display: flex;
          align-items: center;
          gap: var(--space-12);
        ">
          <div style="
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 16px rgba(var(--color-success-rgb), 0.3);
          ">
            <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
            </svg>
          </div>
          Top Performing Data Sources
        </h2>
        <div style="
          display: flex;
          align-items: center;
          gap: var(--space-8);
          padding: var(--space-8) var(--space-16);
          background: rgba(var(--color-success-rgb), 0.1);
          border: 1px solid rgba(var(--color-success-rgb), 0.2);
          border-radius: var(--radius-full);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          color: var(--color-success);
        ">
          <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          By Record Volume
        </div>
      </div>

      <div style="
        position: relative;
        z-index: 1;
      ">
        <% if @top_data_sources.any? %>
          <div style="display: grid; gap: var(--space-16);">
            <% @top_data_sources.each_with_index do |(name, records), index| %>
              <% rank_colors = [
                { bg: 'var(--color-warning)', rgb: 'var(--color-warning-rgb)', label: 'Gold' },
                { bg: 'var(--color-text-secondary)', rgb: 'var(--color-text-secondary-rgb)', label: 'Silver' },
                { bg: 'var(--color-error)', rgb: 'var(--color-error-rgb)', label: 'Bronze' },
                { bg: 'var(--color-primary)', rgb: 'var(--color-primary-rgb)', label: 'Top' },
                { bg: 'var(--color-info)', rgb: 'var(--color-info-rgb)', label: 'High' }
              ] %>
              <% color = rank_colors[index] || rank_colors.last %>
              <div style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: var(--space-20);
                background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.8) 0%, rgba(<%= color[:rgb] %>, 0.02) 100%);
                border: 1px solid rgba(var(--color-border-rgb), 0.3);
                border-left: 4px solid <%= color[:bg] %>;
                border-radius: var(--radius-lg);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                position: relative;
                overflow: hidden;
              " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 24px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                <!-- Rank indicator -->
                <div style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 60px;
                  height: 60px;
                  background: linear-gradient(135deg, rgba(<%= color[:rgb] %>, 0.1) 0%, transparent 100%);
                  border-radius: 0 var(--radius-lg) 0 100%;
                "></div>

                <div style="display: flex; align-items: center; gap: var(--space-20);">
                  <div style="
                    width: 56px;
                    height: 56px;
                    background: linear-gradient(135deg, <%= color[:bg] %> 0%, rgba(<%= color[:rgb] %>, 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: var(--font-weight-bold);
                    color: white;
                    font-size: var(--font-size-lg);
                    box-shadow: 0 6px 12px rgba(<%= color[:rgb] %>, 0.3);
                    position: relative;
                  ">
                    #<%= index + 1 %>
                    <% if index == 0 %>
                      <div style="
                        position: absolute;
                        top: -4px;
                        right: -4px;
                        width: 16px;
                        height: 16px;
                        background: var(--color-warning);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 8px;
                      ">👑</div>
                    <% end %>
                  </div>

                  <div style="flex: 1;">
                    <h4 style="
                      font-size: var(--font-size-lg);
                      font-weight: var(--font-weight-bold);
                      color: var(--color-text);
                      margin: 0 0 var(--space-4) 0;
                    "><%= name %></h4>
                    <div style="
                      display: flex;
                      align-items: center;
                      gap: var(--space-16);
                      font-size: var(--font-size-sm);
                      color: var(--color-text-secondary);
                    ">
                      <div style="
                        display: flex;
                        align-items: center;
                        gap: var(--space-4);
                      ">
                        <svg style="width: 14px; height: 14px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <%= color[:label] %> Performer
                      </div>
                      <div style="
                        padding: var(--space-2) var(--space-8);
                        background: rgba(<%= color[:rgb] %>, 0.1);
                        border: 1px solid rgba(<%= color[:rgb] %>, 0.2);
                        border-radius: var(--radius-full);
                        font-size: var(--font-size-xs);
                        font-weight: var(--font-weight-medium);
                        color: <%= color[:bg] %>;
                      ">Rank #<%= index + 1 %></div>
                    </div>
                  </div>
                </div>

                <div style="
                  text-align: right;
                  position: relative;
                  z-index: 1;
                ">
                  <div style="
                    font-size: var(--font-size-2xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--color-text);
                    font-variant-numeric: tabular-nums;
                    margin-bottom: var(--space-4);
                  "><%= number_with_delimiter(records) %></div>
                  <div style="
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    font-weight: var(--font-weight-medium);
                  ">Records Processed</div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div style="
            text-align: center;
            padding: var(--space-48) var(--space-24);
            position: relative;
            z-index: 1;
          ">
            <div style="
              width: 80px;
              height: 80px;
              margin: 0 auto var(--space-24) auto;
              background: linear-gradient(135deg, rgba(var(--color-text-secondary-rgb), 0.1) 0%, rgba(var(--color-text-secondary-rgb), 0.05) 100%);
              border: 2px solid rgba(var(--color-text-secondary-rgb), 0.2);
              border-radius: var(--radius-xl);
              display: flex;
              align-items: center;
              justify-content: center;
            ">
              <svg style="width: 40px; height: 40px; color: var(--color-text-secondary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <h3 style="
              font-size: var(--font-size-xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-12) 0;
            ">No Performance Data Available</h3>
            <p style="
              font-size: var(--font-size-lg);
              color: var(--color-text-secondary);
              margin: 0;
              max-width: 400px;
              margin-left: auto;
              margin-right: auto;
            ">No data sources have processed records in the selected time period. Connect data sources to see performance analytics.</p>
          </div>
        <% end %>
      </div>
    </div>
    </div>

    <!-- Executive Activity Analytics & System Health -->
    <div style="
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
      gap: var(--space-32);
      margin-bottom: var(--space-48);
    ">
      <!-- Daily Activity Analytics -->
      <div style="
        background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
        border: 1px solid rgba(var(--color-border-rgb), 0.2);
        border-radius: var(--radius-xl);
        padding: var(--space-32);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
      ">
        <!-- Background Pattern -->
        <div style="
          position: absolute;
          top: 0;
          right: 0;
          width: 150px;
          height: 150px;
          background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.05) 0%, transparent 70%);
          border-radius: 50%;
          transform: translate(30%, -30%);
        "></div>

        <div style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--space-24);
          position: relative;
          z-index: 1;
        ">
          <h3 style="
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-12);
          ">
            <div style="
              width: 40px;
              height: 40px;
              background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
              border-radius: var(--radius-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 6px 12px rgba(var(--color-primary-rgb), 0.3);
            ">
              <svg style="width: 20px; height: 20px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            Daily Activity Trends
          </h3>
          <div style="
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
          ">Last <%= @daily_activity.count %> days</div>
        </div>

        <div style="
          position: relative;
          z-index: 1;
        ">
          <% if @daily_activity.any? %>
            <!-- Summary Stats -->
            <div style="
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: var(--space-12);
              margin-bottom: var(--space-20);
            ">
              <div style="
                text-align: center;
                padding: var(--space-12);
                background: rgba(var(--color-primary-rgb), 0.05);
                border: 1px solid rgba(var(--color-primary-rgb), 0.1);
                border-radius: var(--radius-lg);
              ">
                <div style="
                  font-size: var(--font-size-lg);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                "><%= number_with_delimiter(@daily_activity.values.sum) %></div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                  text-transform: uppercase;
                  letter-spacing: 0.05em;
                ">Total Records</div>
              </div>
              <div style="
                text-align: center;
                padding: var(--space-12);
                background: rgba(var(--color-success-rgb), 0.05);
                border: 1px solid rgba(var(--color-success-rgb), 0.1);
                border-radius: var(--radius-lg);
              ">
                <div style="
                  font-size: var(--font-size-lg);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                "><%= number_with_delimiter(@daily_activity.values.max || 0) %></div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                  text-transform: uppercase;
                  letter-spacing: 0.05em;
                ">Peak Day</div>
              </div>
              <div style="
                text-align: center;
                padding: var(--space-12);
                background: rgba(var(--color-info-rgb), 0.05);
                border: 1px solid rgba(var(--color-info-rgb), 0.1);
                border-radius: var(--radius-lg);
              ">
                <div style="
                  font-size: var(--font-size-lg);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                "><%= (@daily_activity.values.sum.to_f / [@daily_activity.count, 1].max).round %></div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                  text-transform: uppercase;
                  letter-spacing: 0.05em;
                ">Daily Avg</div>
              </div>
            </div>

            <!-- Compact Scrollable Activity List -->
            <div style="
              max-height: 180px;
              overflow-y: auto;
              border: 1px solid rgba(var(--color-border-rgb), 0.2);
              border-radius: var(--radius-lg);
              background: rgba(var(--color-surface-rgb), 0.5);
            ">
              <% max_activity = @daily_activity.values.max %>
              <% recent_activity = @daily_activity.to_a.last(7) %>
              <% recent_activity.each_with_index do |(date, count), index| %>
                <% percentage = max_activity > 0 ? (count.to_f / max_activity * 100).round(1) : 0 %>
                <div style="
                  display: flex;
                  align-items: center;
                  gap: var(--space-12);
                  padding: var(--space-8) var(--space-12);
                  border-bottom: <%= index < (recent_activity.length - 1) ? '1px solid rgba(var(--color-border-rgb), 0.1)' : 'none' %>;
                  transition: background-color 0.2s;
                " onmouseover="this.style.backgroundColor='rgba(var(--color-primary-rgb), 0.03)'" onmouseout="this.style.backgroundColor='transparent'">
                  <div style="
                    font-size: var(--font-size-xs);
                    font-weight: var(--font-weight-medium);
                    color: var(--color-text-secondary);
                    min-width: 40px;
                  "><%= date.strftime('%m/%d') %></div>

                  <div style="
                    flex: 1;
                    height: 6px;
                    background: rgba(var(--color-border-rgb), 0.2);
                    border-radius: var(--radius-full);
                    overflow: hidden;
                  ">
                    <div style="
                      width: <%= percentage %>%;
                      height: 100%;
                      background: linear-gradient(90deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
                      border-radius: var(--radius-full);
                      transition: width 0.3s ease;
                    "></div>
                  </div>

                  <div style="
                    font-size: var(--font-size-sm);
                    font-weight: var(--font-weight-medium);
                    color: var(--color-text);
                    min-width: 50px;
                    text-align: right;
                    font-variant-numeric: tabular-nums;
                  "><%= number_with_delimiter(count) %></div>
                </div>
              <% end %>
            </div>

            <% if @daily_activity.count > 7 %>
              <div style="
                text-align: center;
                margin-top: var(--space-12);
              ">
                <button style="
                  font-size: var(--font-size-xs);
                  color: var(--color-primary);
                  background: none;
                  border: none;
                  cursor: pointer;
                  text-decoration: underline;
                ">View all <%= @daily_activity.count %> days</button>
              </div>
            <% end %>
          <% else %>
            <div style="
              text-align: center;
              padding: var(--space-24);
              color: var(--color-text-secondary);
            ">
              <svg style="width: 32px; height: 32px; margin: 0 auto var(--space-12) auto;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              <p style="
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
              ">No activity data available</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Executive System Health Monitor -->
      <div style="
        background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-success-rgb), 0.02) 100%);
        border: 1px solid rgba(var(--color-border-rgb), 0.2);
        border-radius: var(--radius-xl);
        padding: var(--space-32);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
      ">
        <!-- Background Pattern -->
        <div style="
          position: absolute;
          top: 0;
          right: 0;
          width: 150px;
          height: 150px;
          background: radial-gradient(circle, rgba(var(--color-success-rgb), 0.05) 0%, transparent 70%);
          border-radius: 50%;
          transform: translate(30%, -30%);
        "></div>

        <div style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--space-24);
          position: relative;
          z-index: 1;
        ">
          <h3 style="
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-12);
          ">
            <div style="
              width: 40px;
              height: 40px;
              background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
              border-radius: var(--radius-lg);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 6px 12px rgba(var(--color-success-rgb), 0.3);
            ">
              <svg style="width: 20px; height: 20px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
              </svg>
            </div>
            System Health Monitor
          </h3>
          <div style="
            display: flex;
            align-items: center;
            gap: var(--space-8);
            padding: var(--space-8) var(--space-16);
            background: rgba(var(--color-success-rgb), 0.1);
            border: 1px solid rgba(var(--color-success-rgb), 0.2);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-success);
          ">
            <div style="
              width: 8px;
              height: 8px;
              background: var(--color-success);
              border-radius: 50%;
              animation: pulse 2s infinite;
            "></div>
            <%= @failed_jobs == 0 ? 'All Systems Operational' : 'Issues Detected' %>
          </div>
        </div>

        <div style="
          display: grid;
          gap: var(--space-16);
          position: relative;
          z-index: 1;
        ">
          <!-- Active Sources Health -->
          <% active_percentage = @total_data_sources > 0 ? (@active_data_sources.to_f / @total_data_sources * 100).round(1) : 0 %>
          <div style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-16);
            background: rgba(var(--color-surface-rgb), 0.8);
            border: 1px solid rgba(var(--color-border-rgb), 0.3);
            border-left: 4px solid var(--color-success);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
          ">
            <div style="display: flex; align-items: center; gap: var(--space-12);">
              <div style="
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 8px rgba(var(--color-success-rgb), 0.3);
              ">
                <svg style="width: 16px; height: 16px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
              </div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Active Sources</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                "><%= active_percentage %>% operational</div>
              </div>
            </div>
            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
            ">
              <div style="
                width: 100px;
                height: 8px;
                background: rgba(var(--color-border-rgb), 0.2);
                border-radius: var(--radius-full);
                overflow: hidden;
              ">
                <div style="
                  width: <%= active_percentage %>%;
                  height: 100%;
                  background: linear-gradient(90deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                  border-radius: var(--radius-full);
                  transition: width 0.3s ease;
                "></div>
              </div>
              <div style="
                font-size: var(--font-size-lg);
                font-weight: var(--font-weight-bold);
                color: var(--color-text);
                font-variant-numeric: tabular-nums;
              "><%= @active_data_sources %></div>
            </div>
          </div>

          <!-- Pending Sources -->
          <% pending_sources = [@total_data_sources - @active_data_sources - @failed_jobs, 0].max %>
          <% pending_percentage = @total_data_sources > 0 ? (pending_sources.to_f / @total_data_sources * 100).round(1) : 0 %>
          <div style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-16);
            background: rgba(var(--color-surface-rgb), 0.8);
            border: 1px solid rgba(var(--color-border-rgb), 0.3);
            border-left: 4px solid var(--color-warning);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
          ">
            <div style="display: flex; align-items: center; gap: var(--space-12);">
              <div style="
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, var(--color-warning) 0%, rgba(var(--color-warning-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 8px rgba(var(--color-warning-rgb), 0.3);
              ">
                <svg style="width: 16px; height: 16px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Pending Sources</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                "><%= pending_percentage %>% pending setup</div>
              </div>
            </div>
            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
            ">
              <div style="
                width: 100px;
                height: 8px;
                background: rgba(var(--color-border-rgb), 0.2);
                border-radius: var(--radius-full);
                overflow: hidden;
              ">
                <div style="
                  width: <%= pending_percentage %>%;
                  height: 100%;
                  background: linear-gradient(90deg, var(--color-warning) 0%, rgba(var(--color-warning-rgb), 0.8) 100%);
                  border-radius: var(--radius-full);
                  transition: width 0.3s ease;
                "></div>
              </div>
              <div style="
                font-size: var(--font-size-lg);
                font-weight: var(--font-weight-bold);
                color: var(--color-text);
                font-variant-numeric: tabular-nums;
              "><%= pending_sources %></div>
            </div>
          </div>

          <!-- Failed Sources -->
          <% failed_percentage = @total_data_sources > 0 ? (@failed_jobs.to_f / @total_data_sources * 100).round(1) : 0 %>
          <div style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-16);
            background: rgba(var(--color-surface-rgb), 0.8);
            border: 1px solid rgba(var(--color-border-rgb), 0.3);
            border-left: 4px solid var(--color-error);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
          ">
            <div style="display: flex; align-items: center; gap: var(--space-12);">
              <div style="
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, var(--color-error) 0%, rgba(var(--color-error-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 8px rgba(var(--color-error-rgb), 0.3);
              ">
                <svg style="width: 16px; height: 16px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Failed Sources</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                "><%= failed_percentage %>% require attention</div>
              </div>
            </div>
            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
            ">
              <div style="
                width: 100px;
                height: 8px;
                background: rgba(var(--color-border-rgb), 0.2);
                border-radius: var(--radius-full);
                overflow: hidden;
              ">
                <div style="
                  width: <%= failed_percentage %>%;
                  height: 100%;
                  background: linear-gradient(90deg, var(--color-error) 0%, rgba(var(--color-error-rgb), 0.8) 100%);
                  border-radius: var(--radius-full);
                  transition: width 0.3s ease;
                "></div>
              </div>
              <div style="
                font-size: var(--font-size-lg);
                font-weight: var(--font-weight-bold);
                color: var(--color-text);
                font-variant-numeric: tabular-nums;
              "><%= @failed_jobs %></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Executive Alerts & Issues Management -->
    <div class="executive-alerts-section" style="
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(<%= @recent_errors.any? ? 'var(--color-error-rgb)' : 'var(--color-success-rgb)' %>, 0.02) 100%);
      border: 1px solid rgba(var(--color-border-rgb), 0.2);
      border-radius: var(--radius-xl);
      padding: var(--space-32);
      margin-bottom: var(--space-48);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      position: relative;
      overflow: hidden;
    ">
      <!-- Background Pattern -->
      <div style="
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(<%= @recent_errors.any? ? 'var(--color-error-rgb)' : 'var(--color-success-rgb)' %>, 0.05) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
      "></div>

      <div style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-32);
        position: relative;
        z-index: 1;
      ">
        <h2 style="
          font-size: var(--font-size-2xl);
          font-weight: var(--font-weight-bold);
          color: var(--color-text);
          margin: 0;
          display: flex;
          align-items: center;
          gap: var(--space-12);
        ">
          <div style="
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, <%= @recent_errors.any? ? 'var(--color-error)' : 'var(--color-success)' %> 0%, rgba(<%= @recent_errors.any? ? 'var(--color-error-rgb)' : 'var(--color-success-rgb)' %>, 0.8) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 16px rgba(<%= @recent_errors.any? ? 'var(--color-error-rgb)' : 'var(--color-success-rgb)' %>, 0.3);
          ">
            <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <% if @recent_errors.any? %>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              <% else %>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              <% end %>
            </svg>
          </div>
          <%= @recent_errors.any? ? 'System Alerts & Issues' : 'System Status' %>
        </h2>
        <div style="
          display: flex;
          align-items: center;
          gap: var(--space-8);
          padding: var(--space-8) var(--space-16);
          background: rgba(<%= @recent_errors.any? ? 'var(--color-error-rgb)' : 'var(--color-success-rgb)' %>, 0.1);
          border: 1px solid rgba(<%= @recent_errors.any? ? 'var(--color-error-rgb)' : 'var(--color-success-rgb)' %>, 0.2);
          border-radius: var(--radius-full);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          color: <%= @recent_errors.any? ? 'var(--color-error)' : 'var(--color-success)' %>;
        ">
          <% if @recent_errors.any? %>
            <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <%= @recent_errors.count %> <%= @recent_errors.count == 1 ? 'Issue' : 'Issues' %> Detected
          <% else %>
            <div style="
              width: 8px;
              height: 8px;
              background: var(--color-success);
              border-radius: 50%;
              animation: pulse 2s infinite;
            "></div>
            All Systems Operational
          <% end %>
        </div>
      </div>

      <div style="
        position: relative;
        z-index: 1;
      ">
        <% if @recent_errors.any? %>
          <div style="display: grid; gap: var(--space-16);">
            <% @recent_errors.each_with_index do |job, index| %>
              <div style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: var(--space-20);
                background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.8) 0%, rgba(var(--color-error-rgb), 0.02) 100%);
                border: 1px solid rgba(var(--color-border-rgb), 0.3);
                border-left: 4px solid var(--color-error);
                border-radius: var(--radius-lg);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                position: relative;
                overflow: hidden;
              " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 24px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                <!-- Error indicator -->
                <div style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 60px;
                  height: 60px;
                  background: linear-gradient(135deg, rgba(var(--color-error-rgb), 0.1) 0%, transparent 100%);
                  border-radius: 0 var(--radius-lg) 0 100%;
                "></div>

                <div style="display: flex; align-items: center; gap: var(--space-20);">
                  <div style="
                    width: 56px;
                    height: 56px;
                    background: linear-gradient(135deg, var(--color-error) 0%, rgba(var(--color-error-rgb), 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 6px 12px rgba(var(--color-error-rgb), 0.3);
                    position: relative;
                  ">
                    <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                  </div>

                  <div style="flex: 1;">
                    <h4 style="
                      font-size: var(--font-size-lg);
                      font-weight: var(--font-weight-bold);
                      color: var(--color-text);
                      margin: 0 0 var(--space-4) 0;
                    "><%= job.data_source.name %></h4>
                    <p style="
                      font-size: var(--font-size-sm);
                      color: var(--color-text-secondary);
                      margin: 0 0 var(--space-8) 0;
                      line-height: 1.4;
                    "><%= job.error_message.presence || 'Connection failed - unable to establish connection to data source' %></p>
                    <div style="
                      display: flex;
                      align-items: center;
                      gap: var(--space-16);
                      font-size: var(--font-size-xs);
                      color: var(--color-text-secondary);
                    ">
                      <div style="
                        display: flex;
                        align-items: center;
                        gap: var(--space-4);
                      ">
                        <svg style="width: 12px; height: 12px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <%= time_ago_in_words(job.created_at) %> ago
                      </div>
                      <div style="
                        padding: var(--space-2) var(--space-8);
                        background: rgba(var(--color-error-rgb), 0.1);
                        border: 1px solid rgba(var(--color-error-rgb), 0.2);
                        border-radius: var(--radius-full);
                        font-weight: var(--font-weight-medium);
                        color: var(--color-error);
                      ">Critical</div>
                    </div>
                  </div>
                </div>

                <div style="
                  display: flex;
                  align-items: center;
                  gap: var(--space-8);
                  position: relative;
                  z-index: 1;
                ">
                  <button class="btn btn--outline btn--sm" style="
                    background: rgba(var(--color-surface-rgb), 0.8);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(var(--color-error-rgb), 0.3);
                    color: var(--color-error);
                    font-size: var(--font-size-xs);
                  ">Retry</button>
                  <button class="btn btn--outline btn--sm" style="
                    background: rgba(var(--color-surface-rgb), 0.8);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(var(--color-border-rgb), 0.3);
                    font-size: var(--font-size-xs);
                  ">Details</button>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div style="
            text-align: center;
            padding: var(--space-48) var(--space-24);
            position: relative;
            z-index: 1;
          ">
            <div style="
              width: 80px;
              height: 80px;
              margin: 0 auto var(--space-24) auto;
              background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.1) 0%, rgba(var(--color-success-rgb), 0.05) 100%);
              border: 2px solid rgba(var(--color-success-rgb), 0.2);
              border-radius: var(--radius-xl);
              display: flex;
              align-items: center;
              justify-content: center;
            ">
              <svg style="width: 40px; height: 40px; color: var(--color-success);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 style="
              font-size: var(--font-size-xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-12) 0;
            ">All Systems Operational</h3>
            <p style="
              font-size: var(--font-size-lg);
              color: var(--color-text-secondary);
              margin: 0;
              max-width: 400px;
              margin-left: auto;
              margin-right: auto;
            ">No errors or issues detected in the selected time period. All data sources are functioning normally.</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Executive Business Intelligence Section -->
    <% if @ecommerce_insights.present? && @ecommerce_insights.any? %>
      <div class="executive-business-intelligence-section" style="
        background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
        border: 1px solid rgba(var(--color-border-rgb), 0.2);
        border-radius: var(--radius-xl);
        padding: var(--space-32);
        margin-bottom: var(--space-48);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
      ">
        <!-- Background Pattern -->
        <div style="
          position: absolute;
          top: 0;
          right: 0;
          width: 250px;
          height: 250px;
          background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.05) 0%, transparent 70%);
          border-radius: 50%;
          transform: translate(50%, -50%);
        "></div>

        <div style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: var(--space-32);
          position: relative;
          z-index: 1;
        ">
          <div>
            <h2 style="
              font-size: var(--font-size-2xl);
              font-weight: var(--font-weight-bold);
              color: var(--color-text);
              margin: 0 0 var(--space-8) 0;
              display: flex;
              align-items: center;
              gap: var(--space-12);
            ">
              <div style="
                width: 48px;
                height: 48px;
                background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 8px 16px rgba(var(--color-primary-rgb), 0.3);
              ">
                <svg style="width: 24px; height: 24px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              Advanced Business Intelligence
            </h2>
            <p style="
              font-size: var(--font-size-lg);
              color: var(--color-text-secondary);
              margin: 0;
              font-weight: var(--font-weight-medium);
            ">Deep insights from your e-commerce data and customer behavior analytics</p>
          </div>
          <div style="
            display: flex;
            align-items: center;
            gap: var(--space-8);
            padding: var(--space-8) var(--space-16);
            background: rgba(var(--color-info-rgb), 0.1);
            border: 1px solid rgba(var(--color-info-rgb), 0.2);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--color-info);
          ">
            <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            AI-Powered Insights
          </div>
        </div>

        <!-- Executive Revenue & Growth Metrics -->
        <% if @ecommerce_insights[:total_revenue].present? %>
          <div style="
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-24);
            margin-bottom: var(--space-32);
            position: relative;
            z-index: 1;
          ">
            <!-- Total Revenue -->
            <div style="
              background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-success-rgb), 0.02) 100%);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              border-radius: var(--radius-xl);
              padding: var(--space-24);
              position: relative;
              overflow: hidden;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
            ">
              <div style="
                position: absolute;
                top: 0;
                right: 0;
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.1) 0%, transparent 100%);
                border-radius: 0 var(--radius-xl) 0 100%;
              "></div>
              <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
                <div style="
                  width: 56px;
                  height: 56px;
                  background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                  border-radius: var(--radius-lg);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-shadow: 0 8px 16px rgba(var(--color-success-rgb), 0.3);
                  font-size: var(--font-size-xl);
                ">💰</div>
                <div style="
                  padding: var(--space-4) var(--space-8);
                  background: rgba(var(--color-success-rgb), 0.1);
                  border: 1px solid rgba(var(--color-success-rgb), 0.2);
                  border-radius: var(--radius-full);
                  font-size: var(--font-size-xs);
                  font-weight: var(--font-weight-medium);
                  color: var(--color-success);
                ">Revenue</div>
              </div>
              <div>
                <h3 style="
                  font-size: var(--font-size-sm);
                  color: var(--color-text-secondary);
                  font-weight: var(--font-weight-medium);
                  margin: 0 0 var(--space-8) 0;
                  text-transform: uppercase;
                  letter-spacing: 0.05em;
                ">Total Revenue</h3>
                <p style="
                  font-size: var(--font-size-4xl);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                  margin: 0 0 var(--space-8) 0;
                  line-height: 1;
                  font-variant-numeric: tabular-nums;
                ">$<%= number_with_delimiter(@ecommerce_insights[:total_revenue][:total_revenue], precision: 2) %></p>
                <p style="
                  font-size: var(--font-size-sm);
                  color: var(--color-success);
                  font-weight: var(--font-weight-medium);
                  margin: 0;
                ">AOV: $<%= @ecommerce_insights[:total_revenue][:average_order_value] %></p>
              </div>
            </div>

            <!-- Customer Growth -->
            <% if @ecommerce_insights[:growth_metrics].present? %>
              <% growth = @ecommerce_insights[:growth_metrics][:customer_growth_rate] %>
              <div style="
                background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
                border: 1px solid rgba(var(--color-border-rgb), 0.3);
                border-radius: var(--radius-xl);
                padding: var(--space-24);
                position: relative;
                overflow: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
              ">
                <div style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 80px;
                  height: 80px;
                  background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1) 0%, transparent 100%);
                  border-radius: 0 var(--radius-xl) 0 100%;
                "></div>
                <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
                  <div style="
                    width: 56px;
                    height: 56px;
                    background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 16px rgba(var(--color-primary-rgb), 0.3);
                    font-size: var(--font-size-xl);
                  ">📈</div>
                  <div style="
                    padding: var(--space-4) var(--space-8);
                    background: rgba(<%= growth > 0 ? 'var(--color-success-rgb)' : 'var(--color-warning-rgb)' %>, 0.1);
                    border: 1px solid rgba(<%= growth > 0 ? 'var(--color-success-rgb)' : 'var(--color-warning-rgb)' %>, 0.2);
                    border-radius: var(--radius-full);
                    font-size: var(--font-size-xs);
                    font-weight: var(--font-weight-medium);
                    color: <%= growth > 0 ? 'var(--color-success)' : 'var(--color-warning)' %>;
                  "><%= growth > 0 ? 'Growing' : 'Declining' %></div>
                </div>
                <div>
                  <h3 style="
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    font-weight: var(--font-weight-medium);
                    margin: 0 0 var(--space-8) 0;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                  ">Customer Growth</h3>
                  <p style="
                    font-size: var(--font-size-4xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--color-text);
                    margin: 0 0 var(--space-8) 0;
                    line-height: 1;
                    font-variant-numeric: tabular-nums;
                  "><%= growth > 0 ? '+' : '' %><%= growth %>%</p>
                  <p style="
                    font-size: var(--font-size-sm);
                    color: var(--color-primary);
                    font-weight: var(--font-weight-medium);
                    margin: 0;
                  ">Revenue: <%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] > 0 ? '+' : '' %><%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] %>%</p>
                </div>
              </div>
            <% end %>

            <!-- Market Expansion Score -->
            <% if @ecommerce_insights[:growth_opportunities].present? %>
              <div style="
                background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-info-rgb), 0.02) 100%);
                border: 1px solid rgba(var(--color-border-rgb), 0.3);
                border-radius: var(--radius-xl);
                padding: var(--space-24);
                position: relative;
                overflow: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
              ">
                <div style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 80px;
                  height: 80px;
                  background: linear-gradient(135deg, rgba(var(--color-info-rgb), 0.1) 0%, transparent 100%);
                  border-radius: 0 var(--radius-xl) 0 100%;
                "></div>
                <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
                  <div style="
                    width: 56px;
                    height: 56px;
                    background: linear-gradient(135deg, var(--color-info) 0%, rgba(var(--color-info-rgb), 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 16px rgba(var(--color-info-rgb), 0.3);
                    font-size: var(--font-size-xl);
                  ">🌍</div>
                  <div style="
                    padding: var(--space-4) var(--space-8);
                    background: rgba(var(--color-info-rgb), 0.1);
                    border: 1px solid rgba(var(--color-info-rgb), 0.2);
                    border-radius: var(--radius-full);
                    font-size: var(--font-size-xs);
                    font-weight: var(--font-weight-medium);
                    color: var(--color-info);
                  ">Global</div>
                </div>
                <div>
                  <h3 style="
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    font-weight: var(--font-weight-medium);
                    margin: 0 0 var(--space-8) 0;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                  ">Market Expansion Score</h3>
                  <p style="
                    font-size: var(--font-size-4xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--color-text);
                    margin: 0 0 var(--space-8) 0;
                    line-height: 1;
                    font-variant-numeric: tabular-nums;
                  "><%= @ecommerce_insights[:growth_opportunities][:market_expansion_score] %></p>
                  <p style="
                    font-size: var(--font-size-sm);
                    color: var(--color-info);
                    font-weight: var(--font-weight-medium);
                    margin: 0;
                  ">Markets: <%= @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].count %></p>
                </div>
              </div>
            <% end %>

            <!-- Risk Assessment -->
            <% if @ecommerce_insights[:risk_indicators].present? %>
              <div style="
                background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-warning-rgb), 0.02) 100%);
                border: 1px solid rgba(var(--color-border-rgb), 0.3);
                border-radius: var(--radius-xl);
                padding: var(--space-24);
                position: relative;
                overflow: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
              ">
                <div style="
                  position: absolute;
                  top: 0;
                  right: 0;
                  width: 80px;
                  height: 80px;
                  background: linear-gradient(135deg, rgba(var(--color-warning-rgb), 0.1) 0%, transparent 100%);
                  border-radius: 0 var(--radius-xl) 0 100%;
                "></div>
                <div style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--space-16);">
                  <div style="
                    width: 56px;
                    height: 56px;
                    background: linear-gradient(135deg, var(--color-warning) 0%, rgba(var(--color-warning-rgb), 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 8px 16px rgba(var(--color-warning-rgb), 0.3);
                    font-size: var(--font-size-xl);
                  ">⚠️</div>
                  <div style="
                    padding: var(--space-4) var(--space-8);
                    background: rgba(var(--color-warning-rgb), 0.1);
                    border: 1px solid rgba(var(--color-warning-rgb), 0.2);
                    border-radius: var(--radius-full);
                    font-size: var(--font-size-xs);
                    font-weight: var(--font-weight-medium);
                    color: var(--color-warning);
                  ">Risk</div>
                </div>
                <div>
                  <h3 style="
                    font-size: var(--font-size-sm);
                    color: var(--color-text-secondary);
                    font-weight: var(--font-weight-medium);
                    margin: 0 0 var(--space-8) 0;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                  ">Risk Assessment</h3>
                  <p style="
                    font-size: var(--font-size-4xl);
                    font-weight: var(--font-weight-bold);
                    color: var(--color-text);
                    margin: 0 0 var(--space-8) 0;
                    line-height: 1;
                    text-transform: capitalize;
                  "><%= @ecommerce_insights[:risk_indicators][:risk_score] %></p>
                  <p style="
                    font-size: var(--font-size-sm);
                    color: var(--color-warning);
                    font-weight: var(--font-weight-medium);
                    margin: 0;
                  ">Churn: <%= @ecommerce_insights[:risk_indicators][:customer_churn_risk] %></p>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Executive Detailed Analytics Grid -->
        <div style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
          gap: var(--space-32);
          position: relative;
          z-index: 1;
        ">
          <!-- Top Customer Segments -->
          <% if @ecommerce_insights[:top_performing_segments].present? %>
            <div style="
              background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.02) 100%);
              border: 1px solid rgba(var(--color-border-rgb), 0.2);
              border-radius: var(--radius-xl);
              padding: var(--space-32);
              backdrop-filter: blur(20px);
              -webkit-backdrop-filter: blur(20px);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
              position: relative;
              overflow: hidden;
            ">
              <!-- Background Pattern -->
              <div style="
                position: absolute;
                top: 0;
                right: 0;
                width: 150px;
                height: 150px;
                background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.05) 0%, transparent 70%);
                border-radius: 50%;
                transform: translate(30%, -30%);
              "></div>

              <div style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: var(--space-24);
                position: relative;
                z-index: 1;
              ">
                <h3 style="
                  font-size: var(--font-size-xl);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                  margin: 0;
                  display: flex;
                  align-items: center;
                  gap: var(--space-12);
                ">
                  <div style="
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 6px 12px rgba(var(--color-primary-rgb), 0.3);
                    font-size: var(--font-size-lg);
                  ">👥</div>
                  Top Customer Segments
                </h3>
                <div style="
                  font-size: var(--font-size-sm);
                  color: var(--color-text-secondary);
                ">By Revenue</div>
              </div>

              <div style="
                display: grid;
                gap: var(--space-16);
                position: relative;
                z-index: 1;
              ">
                <% @ecommerce_insights[:top_performing_segments].each_with_index do |(segment, data), index| %>
                  <% rank_colors = [
                    { bg: 'var(--color-warning)', rgb: 'var(--color-warning-rgb)', label: 'Gold' },
                    { bg: 'var(--color-text-secondary)', rgb: 'var(--color-text-secondary-rgb)', label: 'Silver' },
                    { bg: 'var(--color-error)', rgb: 'var(--color-error-rgb)', label: 'Bronze' },
                    { bg: 'var(--color-primary)', rgb: 'var(--color-primary-rgb)', label: 'Top' },
                    { bg: 'var(--color-info)', rgb: 'var(--color-info-rgb)', label: 'High' }
                  ] %>
                  <% color = rank_colors[index] || rank_colors.last %>
                  <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: var(--space-20);
                    background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.8) 0%, rgba(<%= color[:rgb] %>, 0.02) 100%);
                    border: 1px solid rgba(var(--color-border-rgb), 0.3);
                    border-left: 4px solid <%= color[:bg] %>;
                    border-radius: var(--radius-lg);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    backdrop-filter: blur(10px);
                    -webkit-backdrop-filter: blur(10px);
                    position: relative;
                    overflow: hidden;
                  " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 24px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <!-- Rank indicator -->
                    <div style="
                      position: absolute;
                      top: 0;
                      right: 0;
                      width: 60px;
                      height: 60px;
                      background: linear-gradient(135deg, rgba(<%= color[:rgb] %>, 0.1) 0%, transparent 100%);
                      border-radius: 0 var(--radius-lg) 0 100%;
                    "></div>

                    <div style="display: flex; align-items: center; gap: var(--space-20);">
                      <div style="
                        width: 48px;
                        height: 48px;
                        background: linear-gradient(135deg, <%= color[:bg] %> 0%, rgba(<%= color[:rgb] %>, 0.8) 100%);
                        border-radius: var(--radius-lg);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: var(--font-weight-bold);
                        color: white;
                        font-size: var(--font-size-sm);
                        box-shadow: 0 6px 12px rgba(<%= color[:rgb] %>, 0.3);
                        position: relative;
                      ">
                        #<%= index + 1 %>
                        <% if index == 0 %>
                          <div style="
                            position: absolute;
                            top: -4px;
                            right: -4px;
                            width: 16px;
                            height: 16px;
                            background: var(--color-warning);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 8px;
                          ">👑</div>
                        <% end %>
                      </div>

                      <div style="flex: 1;">
                        <h4 style="
                          font-size: var(--font-size-lg);
                          font-weight: var(--font-weight-bold);
                          color: var(--color-text);
                          margin: 0 0 var(--space-4) 0;
                        "><%= segment %></h4>
                        <div style="
                          display: flex;
                          align-items: center;
                          gap: var(--space-16);
                          font-size: var(--font-size-sm);
                          color: var(--color-text-secondary);
                        ">
                          <div style="
                            display: flex;
                            align-items: center;
                            gap: var(--space-4);
                          ">
                            <svg style="width: 14px; height: 14px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                            </svg>
                            <%= data[:order_count] %> orders
                          </div>
                          <div style="
                            display: flex;
                            align-items: center;
                            gap: var(--space-4);
                          ">
                            <svg style="width: 14px; height: 14px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"/>
                            </svg>
                            <%= data[:unique_customers] %> customers
                          </div>
                        </div>
                      </div>
                    </div>

                    <div style="
                      text-align: right;
                      position: relative;
                      z-index: 1;
                    ">
                      <div style="
                        font-size: var(--font-size-xl);
                        font-weight: var(--font-weight-bold);
                        color: var(--color-text);
                        font-variant-numeric: tabular-nums;
                        margin-bottom: var(--space-4);
                      ">$<%= number_with_delimiter(data[:total_revenue], precision: 0) %></div>
                      <div style="
                        font-size: var(--font-size-xs);
                        color: var(--color-text-secondary);
                        font-weight: var(--font-weight-medium);
                      ">AOV: $<%= data[:average_order_value] %></div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Executive Growth Opportunities -->
          <% if @ecommerce_insights[:growth_opportunities].present? %>
            <div style="
              background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-success-rgb), 0.02) 100%);
              border: 1px solid rgba(var(--color-border-rgb), 0.2);
              border-radius: var(--radius-xl);
              padding: var(--space-32);
              backdrop-filter: blur(20px);
              -webkit-backdrop-filter: blur(20px);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
              position: relative;
              overflow: hidden;
            ">
              <!-- Background Pattern -->
              <div style="
                position: absolute;
                top: 0;
                right: 0;
                width: 150px;
                height: 150px;
                background: radial-gradient(circle, rgba(var(--color-success-rgb), 0.05) 0%, transparent 70%);
                border-radius: 50%;
                transform: translate(30%, -30%);
              "></div>

              <div style="
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: var(--space-24);
                position: relative;
                z-index: 1;
              ">
                <h3 style="
                  font-size: var(--font-size-xl);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                  margin: 0;
                  display: flex;
                  align-items: center;
                  gap: var(--space-12);
                ">
                  <div style="
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                    border-radius: var(--radius-lg);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 6px 12px rgba(var(--color-success-rgb), 0.3);
                    font-size: var(--font-size-lg);
                  ">🚀</div>
                  Growth Opportunities
                </h3>
                <div style="
                  display: flex;
                  align-items: center;
                  gap: var(--space-8);
                  padding: var(--space-8) var(--space-16);
                  background: rgba(var(--color-success-rgb), 0.1);
                  border: 1px solid rgba(var(--color-success-rgb), 0.2);
                  border-radius: var(--radius-full);
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-medium);
                  color: var(--color-success);
                ">
                  <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                  </svg>
                  AI Recommendations
                </div>
              </div>

              <div style="
                display: grid;
                gap: var(--space-24);
                position: relative;
                z-index: 1;
              ">
                <!-- Geographic Opportunities -->
                <% if @ecommerce_insights[:growth_opportunities][:geographic_opportunities].present? %>
                  <div style="
                    background: rgba(var(--color-surface-rgb), 0.8);
                    border: 1px solid rgba(var(--color-border-rgb), 0.3);
                    border-radius: var(--radius-lg);
                    padding: var(--space-24);
                    backdrop-filter: blur(10px);
                  ">
                    <h4 style="
                      font-size: var(--font-size-lg);
                      font-weight: var(--font-weight-bold);
                      color: var(--color-text);
                      margin: 0 0 var(--space-16) 0;
                      display: flex;
                      align-items: center;
                      gap: var(--space-8);
                    ">
                      <span style="font-size: var(--font-size-lg);">🌍</span>
                      Top Markets
                    </h4>
                    <div style="display: grid; gap: var(--space-12);">
                      <% @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].each do |country, orders| %>
                        <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          padding: var(--space-16);
                          background: rgba(var(--color-surface-rgb), 0.8);
                          border: 1px solid rgba(var(--color-border-rgb), 0.2);
                          border-radius: var(--radius-lg);
                          transition: all 0.2s;
                        " onmouseover="this.style.background='rgba(var(--color-success-rgb), 0.05)'; this.style.borderColor='rgba(var(--color-success-rgb), 0.2)'" onmouseout="this.style.background='rgba(var(--color-surface-rgb), 0.8)'; this.style.borderColor='rgba(var(--color-border-rgb), 0.2)'">
                          <div style="
                            font-size: var(--font-size-sm);
                            font-weight: var(--font-weight-bold);
                            color: var(--color-text);
                          "><%= country %></div>
                          <div style="
                            font-size: var(--font-size-sm);
                            font-weight: var(--font-weight-bold);
                            color: var(--color-success);
                          "><%= orders %> orders</div>
                        </div>
                      <% end %>
                    </div>
                  </div>
                <% end %>

                <!-- Seasonal Insights -->
                <% if @ecommerce_insights[:growth_opportunities][:seasonal_insights].present? %>
                  <div style="
                    background: rgba(var(--color-surface-rgb), 0.8);
                    border: 1px solid rgba(var(--color-border-rgb), 0.3);
                    border-radius: var(--radius-lg);
                    padding: var(--space-24);
                    backdrop-filter: blur(10px);
                  ">
                    <h4 style="
                      font-size: var(--font-size-lg);
                      font-weight: var(--font-weight-bold);
                      color: var(--color-text);
                      margin: 0 0 var(--space-16) 0;
                      display: flex;
                      align-items: center;
                      gap: var(--space-8);
                    ">
                      <span style="font-size: var(--font-size-lg);">📅</span>
                      Peak Months
                    </h4>
                    <div style="
                      display: flex;
                      flex-wrap: wrap;
                      gap: var(--space-8);
                      margin-bottom: var(--space-16);
                    ">
                      <% @ecommerce_insights[:growth_opportunities][:seasonal_insights][:peak_months].each do |month| %>
                        <span style="
                          padding: var(--space-6) var(--space-12);
                          background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.1) 0%, rgba(var(--color-success-rgb), 0.05) 100%);
                          border: 1px solid rgba(var(--color-success-rgb), 0.2);
                          border-radius: var(--radius-lg);
                          font-size: var(--font-size-xs);
                          font-weight: var(--font-weight-bold);
                          color: var(--color-success);
                        ">
                          <%= Date::MONTHNAMES[month] %>
                        </span>
                      <% end %>
                    </div>
                    <div style="
                      font-size: var(--font-size-sm);
                      color: var(--color-text-secondary);
                    ">
                      Seasonality Score: <span style="
                        font-weight: var(--font-weight-bold);
                        color: var(--color-text);
                      "><%= @ecommerce_insights[:growth_opportunities][:seasonal_insights][:seasonality_score] %>%</span>
                    </div>
                  </div>
                <% end %>

                <!-- Cross-sell Opportunities -->
                <% if @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].present? %>
                  <div style="
                    background: rgba(var(--color-surface-rgb), 0.8);
                    border: 1px solid rgba(var(--color-border-rgb), 0.3);
                    border-radius: var(--radius-lg);
                    padding: var(--space-24);
                    backdrop-filter: blur(10px);
                  ">
                    <h4 style="
                      font-size: var(--font-size-lg);
                      font-weight: var(--font-weight-bold);
                      color: var(--color-text);
                      margin: 0 0 var(--space-16) 0;
                      display: flex;
                      align-items: center;
                      gap: var(--space-8);
                    ">
                      <span style="font-size: var(--font-size-lg);">🔗</span>
                      Cross-sell Opportunities
                    </h4>
                    <div style="
                      padding: var(--space-16);
                      background: linear-gradient(135deg, rgba(var(--color-info-rgb), 0.05) 0%, rgba(var(--color-info-rgb), 0.02) 100%);
                      border: 1px solid rgba(var(--color-info-rgb), 0.2);
                      border-radius: var(--radius-lg);
                    ">
                      <span style="
                        font-size: var(--font-size-sm);
                        color: var(--color-text-secondary);
                      ">
                        <span style="
                          font-weight: var(--font-weight-bold);
                          color: var(--color-info);
                        "><%= @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].count %></span> product combinations identified
                      </span>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Executive Predictive Analytics CTA -->
    <div class="executive-predictive-cta-section" style="
      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
      border: 1px solid rgba(var(--color-border-rgb), 0.2);
      border-radius: var(--radius-xl);
      padding: var(--space-48);
      margin-bottom: var(--space-48);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      position: relative;
      overflow: hidden;
    ">
      <!-- Premium Background Pattern -->
      <div style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 20%, rgba(var(--color-primary-rgb), 0.08) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(var(--color-info-rgb), 0.06) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(var(--color-success-rgb), 0.04) 0%, transparent 50%);
      "></div>

      <div style="
        display: grid;
        grid-template-columns: 1fr auto;
        gap: var(--space-48);
        align-items: center;
        position: relative;
        z-index: 1;
      ">
        <!-- CTA Content -->
        <div>
          <div style="
            display: flex;
            align-items: center;
            gap: var(--space-16);
            margin-bottom: var(--space-24);
          ">
            <div style="
              width: 64px;
              height: 64px;
              background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
              border-radius: var(--radius-xl);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 12px 24px rgba(var(--color-primary-rgb), 0.3);
              font-size: var(--font-size-2xl);
            ">🔮</div>
            <div>
              <h2 style="
                font-size: var(--font-size-3xl);
                font-weight: var(--font-weight-bold);
                color: var(--color-text);
                margin: 0 0 var(--space-8) 0;
                line-height: var(--line-height-tight);
              ">Want Advanced Predictions?</h2>
              <div style="
                display: flex;
                align-items: center;
                gap: var(--space-8);
                padding: var(--space-8) var(--space-16);
                background: rgba(var(--color-primary-rgb), 0.1);
                border: 1px solid rgba(var(--color-primary-rgb), 0.2);
                border-radius: var(--radius-full);
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
                color: var(--color-primary);
                width: fit-content;
              ">
                <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                AI-Powered Engine
              </div>
            </div>
          </div>

          <p style="
            font-size: var(--font-size-lg);
            color: var(--color-text-secondary);
            margin: 0 0 var(--space-32) 0;
            line-height: var(--line-height-relaxed);
            max-width: 600px;
          ">
            Explore our AI-powered Predictive Analytics Engine for advanced forecasting, trend analysis, and scenario planning to drive strategic business decisions.
          </p>

          <!-- Premium Features Grid -->
          <div style="
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-16);
            margin-bottom: var(--space-32);
          ">
            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
              padding: var(--space-16);
              background: rgba(var(--color-surface-rgb), 0.8);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              border-radius: var(--radius-lg);
              backdrop-filter: blur(10px);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
              <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--color-success) 0%, rgba(var(--color-success-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--font-size-lg);
                box-shadow: 0 4px 8px rgba(var(--color-success-rgb), 0.3);
              ">📈</div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Demand Forecasting</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                ">Predict future demand</div>
              </div>
            </div>

            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
              padding: var(--space-16);
              background: rgba(var(--color-surface-rgb), 0.8);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              border-radius: var(--radius-lg);
              backdrop-filter: blur(10px);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
              <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--color-info) 0%, rgba(var(--color-info-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--font-size-lg);
                box-shadow: 0 4px 8px rgba(var(--color-info-rgb), 0.3);
              ">🎯</div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Behavior Analysis</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                ">Customer insights</div>
              </div>
            </div>

            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
              padding: var(--space-16);
              background: rgba(var(--color-surface-rgb), 0.8);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              border-radius: var(--radius-lg);
              backdrop-filter: blur(10px);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
              <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--color-warning) 0%, rgba(var(--color-warning-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--font-size-lg);
                box-shadow: 0 4px 8px rgba(var(--color-warning-rgb), 0.3);
              ">💰</div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Revenue Predictions</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                ">Financial forecasting</div>
              </div>
            </div>

            <div style="
              display: flex;
              align-items: center;
              gap: var(--space-12);
              padding: var(--space-16);
              background: rgba(var(--color-surface-rgb), 0.8);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              border-radius: var(--radius-lg);
              backdrop-filter: blur(10px);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 16px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
              <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--color-error) 0%, rgba(var(--color-error-rgb), 0.8) 100%);
                border-radius: var(--radius-lg);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--font-size-lg);
                box-shadow: 0 4px 8px rgba(var(--color-error-rgb), 0.3);
              ">🎲</div>
              <div>
                <div style="
                  font-size: var(--font-size-sm);
                  font-weight: var(--font-weight-bold);
                  color: var(--color-text);
                ">Scenario Planning</div>
                <div style="
                  font-size: var(--font-size-xs);
                  color: var(--color-text-secondary);
                ">Strategic modeling</div>
              </div>
            </div>
          </div>

          <!-- Premium CTA Buttons -->
          <div style="
            display: flex;
            align-items: center;
            gap: var(--space-16);
          ">
            <%= link_to ai_predictions_path, class: "btn btn--primary", style: "
              background: linear-gradient(135deg, var(--color-primary) 0%, rgba(var(--color-primary-rgb), 0.8) 100%);
              box-shadow: 0 8px 16px rgba(var(--color-primary-rgb), 0.3);
              display: inline-flex;
              align-items: center;
              gap: var(--space-8);
              padding: var(--space-16) var(--space-24);
              font-size: var(--font-size-lg);
              font-weight: var(--font-weight-bold);
            " do %>
              <span style="font-size: var(--font-size-lg);">🚀</span>
              Launch Predictive Analytics
            <% end %>
            <button class="btn btn--outline" style="
              background: rgba(var(--color-surface-rgb), 0.8);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(var(--color-border-rgb), 0.3);
              padding: var(--space-16) var(--space-24);
              font-size: var(--font-size-lg);
              display: inline-flex;
              align-items: center;
              gap: var(--space-8);
            ">
              <svg style="width: 20px; height: 20px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Learn More
            </button>
          </div>
        </div>

        <!-- Premium Visual Element -->
        <div style="
          width: 300px;
          height: 200px;
          background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1) 0%, rgba(var(--color-info-rgb), 0.05) 100%);
          border: 1px solid rgba(var(--color-border-rgb), 0.2);
          border-radius: var(--radius-xl);
          padding: var(--space-24);
          backdrop-filter: blur(10px);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          overflow: hidden;
        ">
          <!-- Animated Background Elements -->
          <div style="
            position: absolute;
            top: 20%;
            left: 20%;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.2) 0%, transparent 100%);
            border-radius: 50%;
            animation: float 3s ease-in-out infinite;
          "></div>
          <div style="
            position: absolute;
            bottom: 20%;
            right: 20%;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.2) 0%, transparent 100%);
            border-radius: 50%;
            animation: float 3s ease-in-out infinite reverse;
          "></div>

          <!-- Chart Visualization -->
          <svg style="width: 200px; height: 120px; z-index: 1;" viewBox="0 0 200 120" fill="none">
            <defs>
              <linearGradient id="predictiveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:var(--color-primary);stop-opacity:0.8"/>
                <stop offset="100%" style="stop-color:var(--color-info);stop-opacity:0.4"/>
              </linearGradient>
            </defs>
            <!-- Trend Lines -->
            <path d="M20 90 Q 60 30 100 50 T 180 40" stroke="var(--color-primary)" stroke-width="3" fill="none" opacity="0.8"/>
            <path d="M20 90 Q 60 70 100 60 T 180 55" stroke="var(--color-success)" stroke-width="2" fill="none" opacity="0.6"/>
            <path d="M20 90 Q 60 50 100 45 T 180 35" stroke="var(--color-info)" stroke-width="2" fill="none" stroke-dasharray="5,5" opacity="0.7"/>

            <!-- Data Points -->
            <circle cx="180" cy="40" r="4" fill="var(--color-primary)"/>
            <circle cx="100" cy="50" r="3" fill="var(--color-success)"/>
            <circle cx="60" cy="30" r="3" fill="var(--color-info)"/>

            <!-- Prediction Area -->
            <path d="M140 45 Q 160 35 180 40 Q 190 42 200 38" stroke="var(--color-warning)" stroke-width="2" fill="none" stroke-dasharray="3,3" opacity="0.8"/>
          </svg>
        </div>
      </div>
    </div>
  </div>

</div>

<!-- Enhanced Analytics JavaScript with Premium Styling -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get CSS custom properties for consistent theming
    const getCSS = (property) => getComputedStyle(document.documentElement).getPropertyValue(property).trim();

    // Premium chart defaults for analytics
    Chart.defaults.font.family = getCSS('--font-family-base');
    Chart.defaults.color = getCSS('--color-text-secondary');
    Chart.defaults.borderColor = getCSS('--color-border');
    Chart.defaults.backgroundColor = getCSS('--color-surface');

    // Performance Trends Chart
    const performanceTrendsCtx = document.getElementById('performanceTrendsChart');
    if (performanceTrendsCtx) {
      const gradient = performanceTrendsCtx.getContext('2d').createLinearGradient(0, 0, 0, 200);
      gradient.addColorStop(0, getCSS('--color-success') + '40');
      gradient.addColorStop(1, getCSS('--color-success') + '05');

      new Chart(performanceTrendsCtx, {
        type: 'line',
        data: {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          datasets: [{
            label: 'Success Rate',
            data: [<%= @success_rate * 0.85 %>, <%= @success_rate * 0.92 %>, <%= @success_rate * 0.88 %>, <%= @success_rate %>],
            borderColor: getCSS('--color-success'),
            backgroundColor: gradient,
            borderWidth: 3,
            pointBackgroundColor: getCSS('--color-success'),
            pointBorderColor: getCSS('--color-surface'),
            pointBorderWidth: 3,
            pointRadius: 6,
            pointHoverRadius: 8,
            tension: 0.4,
            fill: true
          }, {
            label: 'Processing Rate',
            data: [<%= @processing_rate * 0.80 %>, <%= @processing_rate * 0.90 %>, <%= @processing_rate * 0.85 %>, <%= @processing_rate %>],
            borderColor: getCSS('--color-primary'),
            backgroundColor: getCSS('--color-primary') + '20',
            borderWidth: 3,
            pointBackgroundColor: getCSS('--color-primary'),
            pointBorderColor: getCSS('--color-surface'),
            pointBorderWidth: 3,
            pointRadius: 6,
            pointHoverRadius: 8,
            tension: 0.4,
            fill: false
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            intersect: false,
            mode: 'index'
          },
          plugins: {
            legend: {
              display: true,
              position: 'bottom',
              labels: {
                usePointStyle: true,
                pointStyle: 'circle',
                padding: 20,
                color: getCSS('--color-text'),
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            },
            tooltip: {
              backgroundColor: getCSS('--color-surface'),
              titleColor: getCSS('--color-text'),
              bodyColor: getCSS('--color-text-secondary'),
              borderColor: getCSS('--color-border'),
              borderWidth: 1,
              cornerRadius: 12,
              padding: 16,
              displayColors: true,
              callbacks: {
                title: function(context) {
                  return context[0].label;
                },
                label: function(context) {
                  return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                }
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              border: {
                display: false
              },
              ticks: {
                color: getCSS('--color-text-secondary'),
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            },
            y: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: getCSS('--color-border') + '40',
                drawBorder: false
              },
              border: {
                display: false
              },
              ticks: {
                color: getCSS('--color-text-secondary'),
                font: {
                  size: 12,
                  weight: '500'
                },
                callback: function(value) {
                  return value + '%';
                }
              }
            }
          }
        }
      });
    }

    // Add premium hover effects to metric cards
    document.querySelectorAll('[style*="transition: all 0.3s cubic-bezier"]').forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px) scale(1.02)';
        this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.12)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = 'none';
      });
    });

    // Enhanced accessibility for interactive elements
    document.querySelectorAll('button, [onclick]').forEach(element => {
      element.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.click();
        }
      });

      if (!element.hasAttribute('tabindex')) {
        element.setAttribute('tabindex', '0');
      }
    });

    console.log('Executive Analytics Dashboard initialized with premium features');
  });

  // Add premium CSS animations
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .executive-analytics-metrics-section > div:nth-child(2) > div {
      animation: fadeInUp 0.6s ease-out;
    }

    .executive-analytics-metrics-section > div:nth-child(2) > div:nth-child(1) { animation-delay: 0.1s; }
    .executive-analytics-metrics-section > div:nth-child(2) > div:nth-child(2) { animation-delay: 0.2s; }
    .executive-analytics-metrics-section > div:nth-child(2) > div:nth-child(3) { animation-delay: 0.3s; }
    .executive-analytics-metrics-section > div:nth-child(2) > div:nth-child(4) { animation-delay: 0.4s; }

    /* Smooth scrollbar styling */
    *::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    *::-webkit-scrollbar-track {
      background: rgba(var(--color-border-rgb), 0.1);
      border-radius: 4px;
    }

    *::-webkit-scrollbar-thumb {
      background: rgba(var(--color-primary-rgb), 0.3);
      border-radius: 4px;
    }

    *::-webkit-scrollbar-thumb:hover {
      background: rgba(var(--color-primary-rgb), 0.5);
    }

    /* Premium floating animation for CTA visual elements */
    @keyframes float {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }

    /* Enhanced hover effects for CTA features */
    .executive-predictive-cta-section [onmouseover] {
      cursor: pointer;
    }

    /* Responsive design for CTA section */
    @media (max-width: 768px) {
      .executive-predictive-cta-section > div {
        grid-template-columns: 1fr !important;
        gap: var(--space-32) !important;
      }

      .executive-predictive-cta-section > div > div:last-child {
        width: 100% !important;
        height: 150px !important;
      }
    }

    /* Dark mode enhancements for glass morphism */
    @media (prefers-color-scheme: dark), [data-color-scheme="dark"] {
      /* Enhanced glass morphism for dark mode */
      .executive-dashboard-header,
      .executive-kpi-metrics-section,
      .executive-business-intelligence-section,
      .executive-predictive-cta-section {
        background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.9) 0%, rgba(var(--color-primary-rgb), 0.08) 100%) !important;
        border: 1px solid rgba(var(--color-border-rgb), 0.4) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
      }

      /* Enhanced card backgrounds for dark mode */
      .executive-kpi-metrics-section > div:last-child > div,
      .executive-business-intelligence-section > div:last-child > div {
        background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.8) 0%, rgba(var(--color-primary-rgb), 0.05) 100%) !important;
        border: 1px solid rgba(var(--color-border-rgb), 0.4) !important;
      }

      /* Enhanced text contrast for dark mode */
      .executive-dashboard-header h1,
      .executive-kpi-metrics-section h2,
      .executive-business-intelligence-section h2 {
        color: var(--color-text) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      /* Enhanced backdrop blur for dark mode */
      .executive-dashboard-header,
      .executive-kpi-metrics-section,
      .executive-business-intelligence-section,
      .executive-predictive-cta-section {
        backdrop-filter: blur(24px) !important;
        -webkit-backdrop-filter: blur(24px) !important;
      }

      /* Enhanced status indicators for dark mode */
      .executive-kpi-metrics-section [style*="pulse"] {
        box-shadow: 0 0 8px rgba(var(--color-success-rgb), 0.6) !important;
      }
    }
  `;
  document.head.appendChild(style);
</script>