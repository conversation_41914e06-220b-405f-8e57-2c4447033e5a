<nav class="navbar" data-controller="landing-navbar">
  <div class="container">
    <div class="navbar__content">
      <!-- Lo<PERSON> and Brand -->
      <%= link_to root_path, class: "navbar__brand" do %>
        <div class="navbar__logo">
          <svg class="navbar__logo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="navbar__title">Data Reflow</span>
      <% end %>

      <!-- Desktop Navigation -->
      <div class="navbar__menu">
        <a href="#features" class="navbar__link">Features</a>
        <a href="#roi" class="navbar__link">ROI</a>
        <a href="#testimonials" class="navbar__link">Testimonials</a>
        <%= link_to about_path, class: "navbar__link" do %>About<% end %>

        <div class="navbar__actions">
          <!-- Premium Theme Toggle for Landing -->
          <div class="navbar__theme-toggle" data-controller="theme-toggle">
            <button type="button"
                    class="premium-theme-toggle-landing"
                    data-action="click->theme-toggle#toggle"
                    data-theme-toggle-target="button"
                    aria-label="Toggle theme"
                    style="
                      position: relative;
                      padding: var(--space-8);
                      background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
                      backdrop-filter: blur(20px);
                      -webkit-backdrop-filter: blur(20px);
                      border: 1px solid rgba(var(--color-border-rgb), 0.2);
                      border-radius: var(--radius-lg);
                      color: var(--color-text-secondary);
                      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                      overflow: hidden;
                      margin-right: var(--space-16);
                    "
                    onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px rgba(0, 0, 0, 0.1)'; this.style.borderColor='rgba(var(--color-primary-rgb), 0.3)'"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.05)'; this.style.borderColor='rgba(var(--color-border-rgb), 0.2)'">

              <!-- Background Pattern -->
              <div style="
                position: absolute;
                top: 0;
                right: 0;
                width: 30px;
                height: 30px;
                background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.1) 0%, transparent 70%);
                border-radius: 50%;
                transform: translate(30%, -30%);
              "></div>

              <!-- Light Mode Icon -->
              <svg data-theme-toggle-target="lightIcon"
                   style="width: 20px; height: 20px; position: relative; z-index: 1;"
                   fill="none"
                   stroke="currentColor"
                   viewBox="0 0 24 24">
                <path stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
              </svg>

              <!-- Dark Mode Icon -->
              <svg data-theme-toggle-target="darkIcon"
                   style="width: 20px; height: 20px; position: relative; z-index: 1; display: none;"
                   fill="none"
                   stroke="currentColor"
                   viewBox="0 0 24 24">
                <path stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
              </svg>
            </button>
          </div>

          <% if user_signed_in? %>
            <%= link_to dashboard_path, class: "navbar__link" do %>
              Dashboard
            <% end %>

            <%= link_to destroy_user_session_path, method: :delete, class: "btn btn--secondary" do %>
              Sign Out
            <% end %>
          <% else %>
            <%= link_to new_user_session_path, class: "navbar__link" do %>
              Sign In
            <% end %>

            <%= link_to new_user_registration_path, class: "btn btn--primary" do %>
              Start Free Trial
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <button class="navbar__toggle" data-action="click->landing-navbar#toggleMobile">
        <span class="navbar__toggle-bar" data-landing-navbar-target="hamburgerTop"></span>
        <span class="navbar__toggle-bar" data-landing-navbar-target="hamburgerMiddle"></span>
        <span class="navbar__toggle-bar" data-landing-navbar-target="hamburgerBottom"></span>
      </button>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div class="navbar__mobile-menu" data-landing-navbar-target="mobileMenu">
    <div class="navbar__mobile-content">
      <a href="#features" class="navbar__mobile-link" data-action="click->landing-navbar#closeMobile">Features</a>
      <a href="#roi" class="navbar__mobile-link" data-action="click->landing-navbar#closeMobile">ROI</a>
      <a href="#testimonials" class="navbar__mobile-link" data-action="click->landing-navbar#closeMobile">Testimonials</a>
      <%= link_to about_path, class: "navbar__mobile-link", data: { action: "click->landing-navbar#closeMobile" } do %>About<% end %>

      <div class="navbar__mobile-actions">
        <!-- Mobile Theme Toggle -->
        <div class="navbar__mobile-theme-toggle" data-controller="theme-toggle" style="margin-bottom: var(--space-16);">
          <button type="button"
                  class="premium-theme-toggle-mobile"
                  data-action="click->theme-toggle#toggle"
                  data-theme-toggle-target="button"
                  aria-label="Toggle theme"
                  style="
                    position: relative;
                    padding: var(--space-12);
                    background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.95) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
                    backdrop-filter: blur(20px);
                    -webkit-backdrop-filter: blur(20px);
                    border: 1px solid rgba(var(--color-border-rgb), 0.2);
                    border-radius: var(--radius-lg);
                    color: var(--color-text-secondary);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                    overflow: hidden;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: var(--space-12);
                  ">

            <!-- Background Pattern -->
            <div style="
              position: absolute;
              top: 0;
              right: 0;
              width: 40px;
              height: 40px;
              background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.1) 0%, transparent 70%);
              border-radius: 50%;
              transform: translate(30%, -30%);
            "></div>

            <!-- Light Mode Icon -->
            <svg data-theme-toggle-target="lightIcon"
                 style="width: 24px; height: 24px; position: relative; z-index: 1;"
                 fill="none"
                 stroke="currentColor"
                 viewBox="0 0 24 24">
              <path stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>

            <!-- Dark Mode Icon -->
            <svg data-theme-toggle-target="darkIcon"
                 style="width: 24px; height: 24px; position: relative; z-index: 1; display: none;"
                 fill="none"
                 stroke="currentColor"
                 viewBox="0 0 24 24">
              <path stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
            </svg>

            <span style="
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--color-text);
              position: relative;
              z-index: 1;
            ">Toggle Theme</span>
          </button>
        </div>

        <% if user_signed_in? %>
          <%= link_to dashboard_path, class: "navbar__mobile-link", data: { action: "click->landing-navbar#closeMobile" } do %>
            Dashboard
          <% end %>

          <%= link_to destroy_user_session_path, method: :delete, class: "btn btn--secondary btn--lg" do %>
            Sign Out
          <% end %>
        <% else %>
          <%= link_to new_user_session_path, class: "navbar__mobile-link" do %>
            Sign In
          <% end %>

          <%= link_to new_user_registration_path, class: "btn btn--primary btn--lg" do %>
            Start Free Trial
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
</nav>