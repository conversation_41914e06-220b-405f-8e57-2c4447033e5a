/* 
 * Data Reflow Design System - Design Tokens
 * 
 * This file contains all the CSS custom properties (design tokens) 
 * used throughout the Data Reflow platform. These tokens ensure 
 * consistency and maintainability across all components.
 */

:root {
  /* ===== COLOR SYSTEM ===== */
  
  /* Primary Brand Colors - Teal */
  --color-primary: #14b8a6;
  --color-primary-hover: #0d9488;
  --color-primary-rgb: 20, 184, 166;

  /* Accent Colors - Teal Variants */
  --color-accent: #5eead4;
  --color-accent-hover: #2dd4bf;
  --color-accent-rgb: 94, 234, 212;
  
  /* Surface & Background Colors */
  --color-background: #f8fafc;
  --color-surface: #ffffff;
  --color-surface-rgb: 255, 255, 253;
  --color-surface-secondary: #f1f5f9;
  
  /* Text Color Hierarchy */
  --color-text: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;
  
  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-rgb: 226, 232, 240;
  --color-border-secondary: #cbd5e1;
  
  /* Status Colors */
  --color-success: #14b8a6;
  --color-success-hover: #0d9488;
  --color-success-rgb: 20, 184, 166;
  
  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  --color-warning-rgb: 245, 158, 11;
  
  --color-error: #ef4444;
  --color-error-hover: #dc2626;
  --color-error-rgb: 239, 68, 68;
  
  --color-info: #3b82f6;
  --color-info-hover: #2563eb;
  --color-info-rgb: 59, 130, 246;
  
  /* Button Specific Colors */
  --color-btn-primary-text: #ffffff;
  --color-btn-secondary-text: var(--color-text);
  
  /* ===== TYPOGRAPHY SYSTEM ===== */
  
  /* Font Size Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  
  /* Font Weight Hierarchy */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* Line Height Standards */
  --line-height-tight: 1.2;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 2;
  
  /* Letter Spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  
  /* ===== SPACING SYSTEM ===== */
  
  /* Spacing Scale (based on 4px grid) */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */
  --space-40: 10rem;    /* 160px */
  --space-48: 12rem;    /* 192px */
  --space-56: 14rem;    /* 224px */
  --space-64: 16rem;    /* 256px */
  
  /* ===== SHADOW SYSTEM ===== */
  
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  
  /* Glass Morphism Shadows */
  --shadow-glass: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
  
  /* ===== BORDER RADIUS SYSTEM ===== */
  
  --radius-none: 0;
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;  /* Fully rounded */
  
  /* ===== ANIMATION & TIMING ===== */
  
  /* Duration */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  
  /* Easing Functions */
  --ease-linear: linear;
  --ease-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* ===== Z-INDEX SCALE ===== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
  
  /* ===== BREAKPOINTS ===== */
  
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* ===== CONTAINER SIZES ===== */
  
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  --container-max: 1200px;
  
  /* ===== COMPONENT SPECIFIC TOKENS ===== */
  
  /* Button Heights */
  --btn-height-sm: 2rem;    /* 32px */
  --btn-height-md: 2.5rem;  /* 40px */
  --btn-height-lg: 3rem;    /* 48px */
  --btn-height-xl: 3.5rem;  /* 56px */
  
  /* Input Heights */
  --input-height-sm: 2rem;    /* 32px */
  --input-height-md: 2.5rem;  /* 40px */
  --input-height-lg: 3rem;    /* 48px */
  
  /* Icon Sizes */
  --icon-size-xs: 0.75rem;  /* 12px */
  --icon-size-sm: 1rem;     /* 16px */
  --icon-size-md: 1.25rem;  /* 20px */
  --icon-size-lg: 1.5rem;   /* 24px */
  --icon-size-xl: 2rem;     /* 32px */
  --icon-size-2xl: 2.5rem;  /* 40px */
  
  /* ===== GRADIENTS ===== */
  
  /* Hero Background Gradient */
  --gradient-hero: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  
  /* Brand Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  --gradient-accent: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-hover) 100%);
  
  /* Status Gradients */
  --gradient-success: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-hover) 100%);
  --gradient-warning: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-hover) 100%);
  --gradient-error: linear-gradient(135deg, var(--color-error) 0%, var(--color-error-hover) 100%);
  
  /* Subtle Background Gradients */
  --gradient-subtle: 
    radial-gradient(circle at 30% 40%, rgba(var(--color-accent-rgb), 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(var(--color-accent-rgb), 0.1) 0%, transparent 50%);
  
  /* ===== GLASS MORPHISM TOKENS ===== */
  
  --glass-background: rgba(var(--color-surface-rgb), 0.8);
  --glass-background-dark: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(var(--color-border-rgb), 0.3);
  --glass-border-light: rgba(255, 255, 255, 0.12);
  --glass-blur: blur(20px);
  
  /* ===== FOCUS RING ===== */
  
  --focus-ring-color: rgba(var(--color-primary-rgb), 0.1);
  --focus-ring-width: 3px;
  --focus-ring-offset: 2px;
}

/* ===== THEME TRANSITION SYSTEM ===== */

/* Smooth theme transitions */
.theme-transitioning,
.theme-transitioning *,
.theme-transitioning *:before,
.theme-transitioning *:after {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Legacy theme transition support */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* ===== DARK MODE IMPLEMENTATION ===== */

/* Dark mode color overrides */
@media (prefers-color-scheme: dark), [data-color-scheme="dark"] {
  :root {
    /* Background & Surface Colors */
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-surface-rgb: 30, 41, 59;
    --color-surface-secondary: #334155;

    /* Text Color Hierarchy */
    --color-text: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-inverse: #1e293b;

    /* Border Colors */
    --color-border: #334155;
    --color-border-rgb: 51, 65, 85;
    --color-border-secondary: #475569;

    /* Primary colors - Teal for brand consistency */
    --color-primary: #2dd4bf;
    --color-primary-hover: #14b8a6;
    --color-primary-rgb: 45, 212, 191;

    /* Accent colors adjusted for dark mode */
    --color-accent: #5eead4;
    --color-accent-hover: #2dd4bf;
    --color-accent-rgb: 94, 234, 212;

    /* Status colors adjusted for better contrast */
    --color-success: #2dd4bf;
    --color-success-hover: #14b8a6;
    --color-success-rgb: 45, 212, 191;

    --color-warning: #fbbf24;
    --color-warning-hover: #f59e0b;
    --color-warning-rgb: 251, 191, 36;

    --color-error: #f87171;
    --color-error-hover: #ef4444;
    --color-error-rgb: 248, 113, 113;

    --color-info: #60a5fa;
    --color-info-hover: #3b82f6;
    --color-info-rgb: 96, 165, 250;

    /* Button text colors for dark mode */
    --color-btn-primary-text: #ffffff;
    --color-btn-secondary-text: var(--color-text);

    /* Glass morphism adjustments for dark mode */
    --glass-background: rgba(30, 41, 59, 0.8);
    --glass-background-dark: rgba(15, 23, 42, 0.9);
    --glass-border: rgba(51, 65, 85, 0.4);
    --glass-border-light: rgba(255, 255, 255, 0.1);

    /* Focus ring adjustments */
    --focus-ring-color: rgba(var(--color-primary-rgb), 0.2);
  }
}

/* Enhanced dark mode for explicit dark theme */
html[data-color-scheme="dark"],
html.dark,
body.dark {
  color-scheme: dark;
}

/* Premium theme toggle button enhancements */
.premium-theme-toggle,
.premium-theme-toggle-landing,
.premium-theme-toggle-mobile {
  cursor: pointer;
  user-select: none;
}

.premium-theme-toggle:focus,
.premium-theme-toggle-landing:focus,
.premium-theme-toggle-mobile:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.2) !important;
}

/* Dark mode specific enhancements for theme toggle */
@media (prefers-color-scheme: dark), [data-color-scheme="dark"] {
  .premium-theme-toggle,
  .premium-theme-toggle-landing,
  .premium-theme-toggle-mobile {
    background: linear-gradient(135deg, rgba(var(--color-surface-rgb), 0.9) 0%, rgba(var(--color-primary-rgb), 0.08) 100%) !important;
    border: 1px solid rgba(var(--color-border-rgb), 0.4) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  }

  .premium-theme-toggle:hover,
  .premium-theme-toggle-landing:hover,
  .premium-theme-toggle-mobile:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4) !important;
    border-color: rgba(var(--color-primary-rgb), 0.4) !important;
  }
}
