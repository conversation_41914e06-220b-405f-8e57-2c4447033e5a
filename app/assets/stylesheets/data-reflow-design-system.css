/*
 * Data Reflow Design System
 * Unified CSS for enterprise-grade data analytics platform
 * 
 * Table of Contents:
 * 1. Design Tokens (CSS Custom Properties)
 * 2. Base Styles & Typography
 * 3. Component Library
 * 4. Layout & Grid Systems
 * 5. Utility Classes
 * 6. Responsive Design
 * 7. Visual Effects & Animations
 */

/* ===== 1. DESIGN TOKENS ===== */

:root {
  /* === Color System === */
  
  /* Primary Brand Colors - Teal */
  --color-primary: rgba(20, 184, 166, 1);        /* #14b8a6 */
  --color-primary-hover: rgba(13, 148, 136, 1);  /* #0d9488 */
  --color-primary-active: rgba(15, 118, 110, 1); /* #0f766e */
  --color-primary-rgb: 20, 184, 166;

  /* Accent Colors - Teal Variants */
  --color-accent: rgba(94, 234, 212, 1);         /* #5eead4 */
  --color-accent-hover: rgba(45, 212, 191, 1);   /* #2dd4bf */
  --color-accent-rgb: 94, 234, 212;
  
  /* Surface & Background Colors */
  --color-background: rgba(252, 252, 249, 1);    /* #fcfcf9 */
  --color-background-rgb: 252, 252, 249;
  --color-surface: rgba(255, 255, 253, 1);       /* #fffffd */
  --color-surface-rgb: 255, 255, 253;
  --color-surface-secondary: rgba(248, 250, 252, 1); /* #f8fafc */

  /* Text Color Hierarchy */
  --color-text: rgba(19, 52, 59, 1);             /* #13343b */
  --color-text-rgb: 19, 52, 59;
  --color-text-secondary: rgba(98, 108, 113, 1); /* #626c71 */
  --color-text-secondary-rgb: 98, 108, 113;
  --color-text-tertiary: rgba(98, 108, 113, 0.7); /* Muted text */
  --color-text-inverse: rgba(255, 255, 255, 1);  /* #ffffff */

  /* Border Colors */
  --color-border: rgba(94, 82, 64, 0.2);         /* #5e5240 at 20% */
  --color-border-rgb: 94, 82, 64;
  --color-border-secondary: rgba(119, 124, 124, 0.2); /* #777c7c */
  --color-card-border: rgba(94, 82, 64, 0.12);   /* Subtle card borders */
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  
  /* Status Colors */
  --color-success: rgba(20, 184, 166, 1);        /* #14b8a6 */
  --color-success-rgb: 20, 184, 166;
  --color-error: rgba(192, 21, 47, 1);           /* #c0152f */
  --color-error-rgb: 192, 21, 47;
  --color-warning: rgba(168, 75, 47, 1);         /* #a84b2f */
  --color-warning-rgb: 168, 75, 47;
  --color-info: rgba(98, 108, 113, 1);           /* #626c71 */
  --color-info-rgb: 98, 108, 113;
  
  /* Button Colors */
  --color-btn-primary-text: rgba(252, 252, 249, 1); /* #fcfcf9 */
  --color-btn-secondary-text: var(--color-text);
  
  /* Focus & Interaction Colors */
  --color-focus-ring: rgba(20, 184, 166, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);
  
  /* Secondary Colors */
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  
  /* === Typography System === */
  
  /* Font Families */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  
  /* Font Size Scale */
  --font-size-xs: 11px;     /* Small labels, captions */
  --font-size-sm: 12px;     /* Form labels, helper text */
  --font-size-base: 14px;   /* Body text, buttons */
  --font-size-md: 14px;     /* Alias for base */
  --font-size-lg: 16px;     /* Large body text, input text */
  --font-size-xl: 18px;     /* Subheadings, large buttons */
  --font-size-2xl: 20px;    /* Card titles, form titles */
  --font-size-3xl: 24px;    /* Section headings */
  --font-size-4xl: 30px;    /* Page titles, hero text */
  --font-size-5xl: 36px;    /* Large hero text */
  
  /* Font Weight Hierarchy */
  --font-weight-light: 300;
  --font-weight-normal: 400;    /* Body text */
  --font-weight-medium: 500;    /* Labels, secondary headings */
  --font-weight-semibold: 550;  /* Buttons, important text */
  --font-weight-bold: 600;      /* Headings, titles */
  --font-weight-extrabold: 700;
  
  /* Line Height Standards */
  --line-height-tight: 1.2;     /* Headings, titles */
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;    /* Body text, forms */
  --line-height-relaxed: 1.6;
  --line-height-loose: 2;
  
  /* Letter Spacing */
  --letter-spacing-tight: -0.01em; /* Headings */
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  
  /* === Spacing System === */
  
  /* Spacing Scale (4px base grid) */
  --space-0: 0;
  --space-1: 1px;      /* Borders, fine adjustments */
  --space-2: 2px;      /* Small gaps */
  --space-4: 4px;      /* Tight spacing */
  --space-6: 6px;      /* Small padding */
  --space-8: 8px;      /* Standard small spacing */
  --space-10: 10px;    /* Button padding */
  --space-12: 12px;    /* Medium spacing */
  --space-16: 16px;    /* Standard spacing unit */
  --space-20: 20px;    /* Large spacing */
  --space-24: 24px;    /* Section spacing */
  --space-32: 32px;    /* Large section spacing */
  --space-40: 40px;    /* Extra large spacing */
  --space-48: 48px;    /* Hero spacing */
  --space-56: 56px;    /* Large hero spacing */
  --space-64: 64px;    /* Maximum spacing */
  
  /* === Shadow System === */
  
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.03);
  
  /* Glass Morphism Shadows */
  --shadow-glass: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
  --shadow-glass-strong:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
  
  /* === Border Radius System === */
  
  --radius-none: 0;
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 9999px;
  
  /* === Animation & Timing === */
  
  /* Duration */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  
  /* Easing Functions */
  --ease-linear: linear;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* === Layout System === */
  
  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  --container-max: 1400px; /* Auth pages */
  
  /* Z-Index Scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
  
  /* === Common Patterns === */
  
  /* Focus Ring */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  
  /* Status Opacity Levels */
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  
  /* Select Caret Icons */
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  
  /* Glass Morphism Tokens */
  --glass-background: rgba(var(--color-surface-rgb), 0.95);
  --glass-background-dark: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(var(--color-border-rgb), 0.3);
  --glass-border-light: rgba(255, 255, 255, 0.12);
  --glass-blur: blur(20px);
}

/* === Dark Mode Overrides === */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);      /* #1f2121 */
    --color-surface: rgba(38, 40, 40, 1);         /* #262828 */
    --color-surface-rgb: 38, 40, 40;
    --color-text: rgba(245, 245, 245, 1);         /* #f5f5f5 */
    --color-text-secondary: rgba(167, 169, 169, 0.7); /* #a7a9a9 */
    --color-text-tertiary: rgba(167, 169, 169, 0.5);
    --color-border: rgba(119, 124, 124, 0.3);     /* #777c7c */
    --color-border-rgb: 119, 124, 124;
    --color-primary: rgba(45, 212, 191, 1);       /* #2dd4bf */
    --color-primary-hover: rgba(20, 184, 166, 1); /* #14b8a6 */
  }
}

/* Dark mode data attribute override */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-surface-rgb: 38, 40, 40;
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-text-tertiary: rgba(167, 169, 169, 0.5);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-border-rgb: 119, 124, 124;
  --color-primary: rgba(45, 212, 191, 1);
  --color-primary-hover: rgba(20, 184, 166, 1);

  /* Complete RGB variants for dark mode */
  --color-background-rgb: 31, 33, 33;
  --color-text-rgb: 245, 245, 245;
  --color-text-secondary-rgb: 167, 169, 169;
  --color-primary-rgb: 50, 184, 198;
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;

  /* Dark mode semantic colors */
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
}

/* ===== 2. BASE STYLES & TYPOGRAPHY ===== */

/* Font Face Declarations */
@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2') format('woff2');
  font-display: swap;
}

/* Base HTML & Body */
html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Elements */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin: 0 0 var(--space-16) 0;
  color: var(--color-text);
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

p {
  margin: 0 0 var(--space-16) 0;
  line-height: var(--line-height-normal);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

/* Text Utilities */
.text-white {
  color: #ffffff !important;
}

.text-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-brand {
  background: linear-gradient(135deg, #32b8c6 0%, #21808d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Selection Styles */
::selection {
  background: rgba(var(--color-primary-rgb), 0.2);
  color: var(--color-text);
}

::-moz-selection {
  background: rgba(var(--color-primary-rgb), 0.2);
  color: var(--color-text);
}

/* ===== 3. COMPONENT LIBRARY ===== */

/* === Button System === */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: var(--space-1) solid transparent;
  border-radius: var(--radius-base);
  padding: var(--space-8) var(--space-16);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(50, 184, 198, 0.4);
}

/* Primary Button */
.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
}

.btn--primary:active {
  background: var(--color-primary-active);
  border-color: var(--color-primary-active);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(var(--color-primary-rgb), 0.2);
}

/* Secondary Button */
.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
  border-color: var(--color-secondary);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
  border-color: var(--color-secondary-active);
}

/* Outline Button */
.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
  border-color: var(--color-secondary-hover);
}

/* Hero Section Outline Button Override */
.hero .btn--outline {
  border: 1px solid #32b8c6;
  color: #32b8c6;
}

.hero .btn--outline:hover {
  background: #32b8c6;
  color: #1e293b;
}

/* CTA Section Outline Button Override */
.cta .btn--outline {
  border: 1px solid #ffffff;
  color: #ffffff;
}

.cta .btn--outline:hover {
  background: #ffffff;
  color: #1e293b;
}

/* Dark Mode Outline Button */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

/* Button Sizes */
.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--xl {
  padding: var(--space-12) var(--space-24);
  font-size: var(--font-size-xl);
  border-radius: var(--radius-lg);
}

/* Button Modifiers */
.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* === Form Components === */

.form-control {
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
  box-shadow: var(--shadow-xs);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring), var(--shadow-xs);
  background-color: var(--color-surface);
}

.form-control::placeholder {
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-normal);
}

.form-control:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--color-surface-secondary);
}

/* Form Labels */
.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-6);
}

.form-label--required::after {
  content: " *";
  color: var(--color-error);
}

/* Form Groups */
.form-group {
  margin-bottom: var(--space-20);
}

.form-group--inline {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

/* Select Elements */
.form-select {
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-40);
}

[data-color-scheme="dark"] .form-select {
  background-image: var(--select-caret-dark);
}

/* Checkbox & Radio */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.form-check-input {
  width: 16px;
  height: 16px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
  accent-color: var(--color-primary);
  cursor: pointer;
}

.form-check-input:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

.form-check-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  cursor: pointer;
  line-height: var(--line-height-normal);
}

/* Form Validation States */
.form-control--error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(var(--color-error-rgb), 0.1);
}

.form-control--success {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(var(--color-success-rgb), 0.1);
}

.form-feedback {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-4);
}

.form-feedback--error {
  color: var(--color-error);
}

.form-feedback--success {
  color: var(--color-success);
}

.form-feedback--help {
  color: var(--color-text-tertiary);
}

/* === Authentication Page Components === */

/* Auth Page Layout */
.auth-page {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.auth-page__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  z-index: 1;
}

.auth-page__background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 40%, rgba(50, 184, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(50, 184, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.auth-page__container {
  width: 100%;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: var(--space-24);
  position: relative;
  z-index: 2;
}

.auth-page__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-64);
  align-items: center;
  min-height: 80vh;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Auth Page Branding Section */
.auth-page__branding {
  color: var(--color-text-inverse);
  text-align: center;
}

.auth-page__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-48);
}

.auth-page__logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-16);
  box-shadow: 0 8px 24px rgba(33, 128, 141, 0.3);
}

.auth-page__logo-icon svg {
  width: 24px;
  height: 24px;
  color: var(--color-text-inverse);
}

.auth-page__brand-name {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-inverse);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-page__brand-tagline {
  font-size: var(--font-size-sm);
  color: rgba(226, 232, 240, 0.8);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.5px;
}

/* Auth Page Welcome Section */
.auth-page__welcome {
  margin-bottom: var(--space-40);
}

.auth-page__welcome-title {
  font-size: clamp(var(--font-size-3xl), 5vw, var(--font-size-5xl));
  font-weight: var(--font-weight-bold);
  color: var(--color-text-inverse);
  margin-bottom: var(--space-16);
  line-height: var(--line-height-tight);
}

.auth-page__welcome-highlight {
  background: linear-gradient(135deg, #32b8c6 0%, #21808d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-page__welcome-subtitle {
  font-size: var(--font-size-xl);
  color: rgba(226, 232, 240, 0.9);
  line-height: var(--line-height-relaxed);
  font-weight: var(--font-weight-medium);
}

/* Auth Page Features */
.auth-page__features {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.auth-page__feature {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
}

.auth-page__feature-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-12);
  flex-shrink: 0;
}

.auth-page__feature-icon--success {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.1));
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.auth-page__feature-icon--primary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.auth-page__feature-icon--secondary {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(147, 51, 234, 0.1));
  border: 1px solid rgba(147, 51, 234, 0.3);
}

.auth-page__feature-icon svg {
  width: 16px;
  height: 16px;
  color: var(--color-text-inverse);
}

.auth-page__feature-content {
  flex: 1;
}

.auth-page__feature-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-base);
  color: var(--color-text-inverse);
}

.auth-page__feature-description {
  font-size: var(--font-size-sm);
  color: rgba(203, 213, 225, 0.8);
  font-weight: var(--font-weight-medium);
}

/* Auth Page Stats */
.auth-page__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-16);
  padding: var(--space-24);
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: var(--glass-blur);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: var(--radius-xl);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.1) inset;
}

.auth-page__stat {
  text-align: center;
}

.auth-page__stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: #32b8c6;
  margin-bottom: var(--space-4);
  text-shadow: 0 0 20px rgba(50, 184, 198, 0.3);
}

.auth-page__stat-label {
  font-size: var(--font-size-xs);
  color: rgba(203, 213, 225, 0.8);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.5px;
}

/* Auth Page Form Container */
.auth-page__form-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.auth-page__form-card {
  width: 100%;
  max-width: 480px;
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-40);
  box-shadow: var(--shadow-glass-strong);
  position: relative;
  overflow: hidden;
}

.auth-page__form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(33, 128, 141, 0.6), transparent);
  opacity: 0.8;
}

.auth-page__form-card--centered {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

/* Auth Form Header */
.auth-page__form-header {
  text-align: center;
  margin-bottom: var(--space-32);
}

.auth-page__form-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
  background: linear-gradient(135deg, var(--color-text), var(--color-text-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-page__form-subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.auth-page__form-link {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.auth-page__form-link:hover {
  color: var(--color-primary-hover);
}

.auth-page__form-link--small {
  font-size: var(--font-size-sm);
}

/* Auth Form Styling */
.auth-page__form {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.auth-page__field {
  display: flex;
  flex-direction: column;
}

.auth-page__field-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
}

.auth-page__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.auth-page__label-hint {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-normal);
  margin-left: var(--space-4);
}

.auth-page__input-wrapper {
  position: relative;
}

.auth-page__input-icon {
  position: absolute;
  left: var(--space-16);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--color-text-secondary);
  pointer-events: none;
}

.auth-page__input-icon svg {
  width: 100%;
  height: 100%;
}

.auth-page__input {
  width: 100%;
  padding: var(--space-16) var(--space-20) var(--space-16) var(--space-48);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.auth-page__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow:
    0 0 0 3px rgba(33, 128, 141, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.05);
  background: #ffffff;
}

.auth-page__input::placeholder {
  color: var(--color-text-tertiary);
}

.auth-page__input--no-icon {
  padding-left: var(--space-20);
}

.auth-page__field-help {
  margin-top: var(--space-8);
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}

/* Auth Form Options */
.auth-page__form-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-16);
}

.auth-page__checkbox {
  display: flex;
  align-items: flex-start;
  gap: var(--space-8);
}

.auth-page__checkbox-input {
  width: 16px;
  height: 16px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
  accent-color: var(--color-primary);
  margin-top: var(--space-2);
  cursor: pointer;
}

.auth-page__checkbox-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(33, 128, 141, 0.1);
}

.auth-page__checkbox-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  cursor: pointer;
}

/* Submit Button */
.auth-page__submit {
  margin-top: var(--space-8);
}

.auth-page__submit-btn {
  width: 100%;
  justify-content: center;
}

/* Password Strength Indicator */
.auth-page__password-strength {
  margin-top: var(--space-8);
  height: 4px;
  width: 100%;
  background: var(--color-border);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.auth-page__password-strength-bar {
  height: 100%;
  background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
  transition: width var(--duration-normal) var(--ease-standard);
  width: 0%;
}

/* Security Badge */
.auth-page__security {
  margin-top: var(--space-24);
  text-align: center;
}

.auth-page__security-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}

.auth-page__security-badge svg {
  width: 16px;
  height: 16px;
  color: var(--color-success);
}

/* === Navigation Components === */

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: var(--glass-blur);
  border-bottom: 1px solid var(--glass-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.navbar__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 var(--space-16);
}

.navbar__brand {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  text-decoration: none;
  transition: transform var(--duration-fast) var(--ease-standard);
}

.navbar__brand:hover {
  transform: scale(1.02);
}

.navbar__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.navbar__logo-icon {
  width: 24px;
  height: 24px;
  color: var(--color-btn-primary-text);
}

.navbar__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

.navbar__menu {
  display: flex;
  align-items: center;
  gap: var(--space-32);
}

.navbar__link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-fast) var(--ease-standard);
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-md);
}

.navbar__link:hover {
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  margin-left: var(--space-32);
}

/* Mobile Navigation */
.navbar__toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-8);
  gap: 4px;
}

.navbar__toggle-bar {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  border-radius: var(--radius-full);
  transition: all var(--duration-fast) var(--ease-standard);
}

.navbar__mobile-menu {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  transform: translateX(100%);
  transition: transform var(--duration-normal) var(--ease-standard);
  z-index: 99;
}

.navbar__mobile-menu--open {
  transform: translateX(0);
}

.navbar__mobile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: var(--space-32);
  padding: var(--space-32);
}

.navbar__mobile-link {
  color: var(--color-text);
  text-decoration: none;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-fast) var(--ease-standard);
}

.navbar__mobile-link:hover {
  color: var(--color-primary);
}

.navbar__mobile-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-16);
  margin-top: var(--space-32);
}

/* === Landing Page Components === */

.landing-page {
  background: var(--color-background);
}

.landing-layout {
  min-height: 100vh;
  background: var(--color-background);
}

.landing-layout__flash {
  position: relative;
  z-index: 40;
}

/* Hero Section */
.hero {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: var(--color-text-inverse);
  padding: var(--space-32) 0;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 40%, rgba(50, 184, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(50, 184, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-16);
  color: var(--color-text-inverse);
  letter-spacing: var(--letter-spacing-tight);
}

.hero__highlight {
  color: #32b8c6;
}

.hero__subtitle {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-24);
  color: #e2e8f0;
  font-weight: var(--font-weight-medium);
}

.hero__actions {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.hero__metrics {
  display: flex;
  gap: var(--space-24);
}

.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero__img {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: block;
}

/* ===== 4. LAYOUT & GRID SYSTEMS ===== */

/* Container System */
.container {
  width: 100%;
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: 0 var(--space-16);
}

.container--sm {
  max-width: var(--container-sm);
}

.container--md {
  max-width: var(--container-md);
}

.container--lg {
  max-width: var(--container-lg);
}

.container--xl {
  max-width: var(--container-xl);
}

.container--2xl {
  max-width: var(--container-2xl);
}

.container--max {
  max-width: var(--container-max);
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--space-16);
}

.grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid--auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid--auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* Gap Utilities */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }
.gap-12 { gap: var(--space-12); }
.gap-16 { gap: var(--space-16); }
.gap-20 { gap: var(--space-20); }
.gap-24 { gap: var(--space-24); }
.gap-32 { gap: var(--space-32); }

/* Flexbox Utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

/* ===== 5. UTILITY CLASSES ===== */

/* Display Utilities */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Position Utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }
.m-12 { margin: var(--space-12); }
.m-16 { margin: var(--space-16); }
.m-20 { margin: var(--space-20); }
.m-24 { margin: var(--space-24); }
.m-32 { margin: var(--space-32); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }
.mt-12 { margin-top: var(--space-12); }
.mt-16 { margin-top: var(--space-16); }
.mt-20 { margin-top: var(--space-20); }
.mt-24 { margin-top: var(--space-24); }
.mt-32 { margin-top: var(--space-32); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-12 { margin-bottom: var(--space-12); }
.mb-16 { margin-bottom: var(--space-16); }
.mb-20 { margin-bottom: var(--space-20); }
.mb-24 { margin-bottom: var(--space-24); }
.mb-32 { margin-bottom: var(--space-32); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-4 { margin-left: var(--space-4); }
.ml-6 { margin-left: var(--space-6); }
.ml-8 { margin-left: var(--space-8); }
.ml-12 { margin-left: var(--space-12); }
.ml-16 { margin-left: var(--space-16); }
.ml-20 { margin-left: var(--space-20); }
.ml-24 { margin-left: var(--space-24); }
.ml-32 { margin-left: var(--space-32); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-4 { margin-right: var(--space-4); }
.mr-6 { margin-right: var(--space-6); }
.mr-8 { margin-right: var(--space-8); }
.mr-12 { margin-right: var(--space-12); }
.mr-16 { margin-right: var(--space-16); }
.mr-20 { margin-right: var(--space-20); }
.mr-24 { margin-right: var(--space-24); }
.mr-32 { margin-right: var(--space-32); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.p-12 { padding: var(--space-12); }
.p-16 { padding: var(--space-16); }
.p-20 { padding: var(--space-20); }
.p-24 { padding: var(--space-24); }
.p-32 { padding: var(--space-32); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-4 { padding-top: var(--space-4); }
.pt-6 { padding-top: var(--space-6); }
.pt-8 { padding-top: var(--space-8); }
.pt-12 { padding-top: var(--space-12); }
.pt-16 { padding-top: var(--space-16); }
.pt-20 { padding-top: var(--space-20); }
.pt-24 { padding-top: var(--space-24); }
.pt-32 { padding-top: var(--space-32); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-4 { padding-bottom: var(--space-4); }
.pb-6 { padding-bottom: var(--space-6); }
.pb-8 { padding-bottom: var(--space-8); }
.pb-12 { padding-bottom: var(--space-12); }
.pb-16 { padding-bottom: var(--space-16); }
.pb-20 { padding-bottom: var(--space-20); }
.pb-24 { padding-bottom: var(--space-24); }
.pb-32 { padding-bottom: var(--space-32); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--space-1); }
.pl-2 { padding-left: var(--space-2); }
.pl-4 { padding-left: var(--space-4); }
.pl-6 { padding-left: var(--space-6); }
.pl-8 { padding-left: var(--space-8); }
.pl-12 { padding-left: var(--space-12); }
.pl-16 { padding-left: var(--space-16); }
.pl-20 { padding-left: var(--space-20); }
.pl-24 { padding-left: var(--space-24); }
.pl-32 { padding-left: var(--space-32); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--space-1); }
.pr-2 { padding-right: var(--space-2); }
.pr-4 { padding-right: var(--space-4); }
.pr-6 { padding-right: var(--space-6); }
.pr-8 { padding-right: var(--space-8); }
.pr-12 { padding-right: var(--space-12); }
.pr-16 { padding-right: var(--space-16); }
.pr-20 { padding-right: var(--space-20); }
.pr-24 { padding-right: var(--space-24); }
.pr-32 { padding-right: var(--space-32); }

/* Typography Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-success { color: var(--color-success); }
.text-error { color: var(--color-error); }
.text-warning { color: var(--color-warning); }

/* Width & Height Utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* Border Radius Utilities */
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* ===== 6. RESPONSIVE DESIGN ===== */

/* Mobile First Breakpoints */
@media (max-width: 768px) {
  /* Mobile Optimizations */

  .container {
    padding: 0 var(--space-12);
  }

  .auth-page__container {
    padding: var(--space-16);
  }

  .auth-page__content {
    grid-template-columns: 1fr;
    gap: var(--space-32);
    text-align: center;
    padding-top: 100px;
  }

  .auth-page__form-card {
    padding: var(--space-24);
  }

  .auth-page__field-group {
    grid-template-columns: 1fr;
    gap: var(--space-16);
  }

  .auth-page__stats {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .navbar__menu {
    display: none;
  }

  .navbar__toggle {
    display: flex;
  }

  .hero__content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
    text-align: center;
  }

  .hero__title {
    font-size: var(--font-size-3xl);
  }

  .hero__subtitle {
    font-size: var(--font-size-lg);
  }

  .hero__actions {
    flex-direction: column;
    align-items: center;
  }

  .hero__metrics {
    justify-content: center;
  }

  .grid--2,
  .grid--3,
  .grid--4 {
    grid-template-columns: 1fr;
  }

  /* Mobile Typography */
  .text-4xl { font-size: var(--font-size-3xl); }
  .text-3xl { font-size: var(--font-size-2xl); }
  .text-2xl { font-size: var(--font-size-xl); }

  /* Mobile Spacing */
  .m-32 { margin: var(--space-16); }
  .mt-32 { margin-top: var(--space-16); }
  .mb-32 { margin-bottom: var(--space-16); }
  .p-32 { padding: var(--space-16); }
  .pt-32 { padding-top: var(--space-16); }
  .pb-32 { padding-bottom: var(--space-16); }
}

@media (max-width: 1024px) {
  /* Tablet Adjustments */

  .auth-page__content {
    grid-template-columns: 1fr;
    gap: var(--space-48);
  }

  .hero__content {
    grid-template-columns: 1fr;
    gap: var(--space-32);
  }

  .grid--3,
  .grid--4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  /* Desktop Enhancements */

  .auth-page__content {
    grid-template-columns: 1fr 1fr;
  }

  .hero__content {
    grid-template-columns: 1fr 1fr;
  }
}

/* ===== 7. VISUAL EFFECTS & ANIMATIONS ===== */

/* Glass Morphism Effects */
.glass {
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-glass);
}

.glass--dark {
  background: var(--glass-background-dark);
  border: 1px solid var(--glass-border-light);
}

.glass--strong {
  box-shadow: var(--shadow-glass-strong);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-standard);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--duration-fast) var(--ease-standard);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
}

/* Focus Effects */
.focus-ring:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

.focus-outline:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--duration-slow) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.animate-bounce {
  animation: bounce 1s var(--ease-bounce);
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Transition Utilities */
.transition-none {
  transition: none;
}

.transition-all {
  transition: all var(--duration-normal) var(--ease-standard);
}

.transition-colors {
  transition: color var(--duration-fast) var(--ease-standard),
              background-color var(--duration-fast) var(--ease-standard),
              border-color var(--duration-fast) var(--ease-standard);
}

.transition-transform {
  transition: transform var(--duration-normal) var(--ease-standard);
}

.transition-opacity {
  transition: opacity var(--duration-fast) var(--ease-standard);
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Accessibility Utilities */
.focus-visible:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .auth-page__background,
  .hero::before {
    display: none !important;
  }

  .auth-page__form-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  * {
    color: #000 !important;
    background: transparent !important;
  }
}
