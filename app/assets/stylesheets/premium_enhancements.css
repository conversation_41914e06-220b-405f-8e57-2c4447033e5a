/* Premium UI Enhancements for Data Refinery Platform */

/* ===========================
   1. CSS Variables & Theming
   =========================== */
:root {
  /* Premium Color Palette - Teal */
  --color-primary: #14b8a6;
  --color-primary-dark: #0d9488;
  --color-primary-light: #5eead4;

  --color-secondary: #2dd4bf;
  --color-secondary-dark: #14b8a6;
  --color-secondary-light: #99f6e4;
  
  --color-success: #14b8a6;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #14b8a6;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  --gradient-dark: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode variables */
[data-theme="dark"] {
  --color-primary: #60a5fa;
  --color-primary-dark: #3b82f6;
  --color-primary-light: #93bbfc;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* ===========================
   2. Base Typography
   =========================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv11', 'ss01', 'ss03';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fluid typography */
h1 { font-size: clamp(2rem, 5vw, 3rem); }
h2 { font-size: clamp(1.5rem, 4vw, 2.25rem); }
h3 { font-size: clamp(1.25rem, 3vw, 1.875rem); }
h4 { font-size: clamp(1.125rem, 2.5vw, 1.5rem); }

/* Tabular numbers for data */
.tabular-nums {
  font-variant-numeric: tabular-nums;
}

/* ===========================
   3. Premium Components
   =========================== */

/* Glass morphism cards */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

.dark .glass-card {
  background: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 3D card effect */
.card-3d {
  transform-style: preserve-3d;
  transition: transform var(--transition-normal);
}

.card-3d:hover {
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg) scale(1.02);
}

/* Gradient borders */
.gradient-border {
  position: relative;
  background: white;
  border-radius: 1rem;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 1rem;
  padding: 2px;
  background: var(--gradient-primary);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* ===========================
   4. Animations
   =========================== */

/* Skeleton loading */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

/* Pulse animation for live indicators */
@keyframes pulse-dot {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

.pulse-dot {
  animation: pulse-dot 2s infinite;
}

/* Shimmer effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

/* Bounce animation for loading dots */
@keyframes bounce-delay {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.bounce-delay-1 { animation-delay: -0.32s; }
.bounce-delay-2 { animation-delay: -0.16s; }
.bounce-delay-3 { animation-delay: 0; }

/* ===========================
   5. Micro-interactions
   =========================== */

/* Button hover effects */
.btn-premium {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: width var(--transition-normal), height var(--transition-normal);
}

.btn-premium:hover::before {
  width: 300px;
  height: 300px;
}

/* Card hover lift */
.card-hover-lift {
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.card-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Input focus effects */
.input-premium {
  transition: all var(--transition-normal);
  border: 2px solid #e5e7eb;
}

.input-premium:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===========================
   6. Custom Scrollbars
   =========================== */

/* Webkit browsers */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* ===========================
   7. Progress Indicators
   =========================== */

/* Circular progress */
.progress-ring {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}

.progress-ring-circle {
  transition: stroke-dashoffset 1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Linear progress with stripes */
.progress-striped {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(255, 255, 255, 0.1) 10px,
    rgba(255, 255, 255, 0.1) 20px
  );
  background-size: 28px 28px;
  animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 28px 0;
  }
}

/* ===========================
   8. Status Indicators
   =========================== */

/* Status dots with glow */
.status-dot {
  position: relative;
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.success {
  background: var(--color-success);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-dot.warning {
  background: var(--color-warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.status-dot.danger {
  background: var(--color-danger);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-dot.info {
  background: var(--color-info);
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}

/* ===========================
   9. Timeline Styles
   =========================== */

.timeline-item {
  position: relative;
  padding-left: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  bottom: -2rem;
  width: 2px;
  background: #e5e7eb;
}

.timeline-item:last-child::before {
  bottom: 0.5rem;
}

.timeline-dot {
  position: absolute;
  left: -6px;
  top: 0.5rem;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: white;
  border: 2px solid var(--color-primary);
  z-index: 1;
}

/* ===========================
   10. Data Tables
   =========================== */

.table-premium {
  border-collapse: separate;
  border-spacing: 0;
}

.table-premium thead th {
  background: #f8fafc;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  color: #64748b;
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-premium tbody tr {
  transition: all var(--transition-fast);
}

.table-premium tbody tr:hover {
  background: #f8fafc;
  transform: scale(1.01);
  box-shadow: var(--shadow-md);
}

/* ===========================
   11. Charts & Visualizations
   =========================== */

.chart-container {
  position: relative;
  height: 300px;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.chart-tooltip.show {
  opacity: 1;
}

/* ===========================
   12. Mobile Optimizations
   =========================== */

@media (max-width: 768px) {
  /* Touch-friendly tap targets */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Swipeable cards */
  .swipe-container {
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }
  
  .swipe-card {
    scroll-snap-align: start;
    flex-shrink: 0;
  }
  
  /* Bottom sheet style modals */
  .modal-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 1rem 1rem 0 0;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
  }
  
  .modal-mobile.show {
    transform: translateY(0);
  }
}

/* ===========================
   13. Dark Mode Enhancements
   =========================== */

.dark {
  color-scheme: dark;
}

.dark .glass-card {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(148, 163, 184, 0.1);
}

.dark .skeleton {
  background: linear-gradient(
    90deg,
    #1e293b 25%,
    #334155 50%,
    #1e293b 75%
  );
}

/* ===========================
   14. Print Styles
   =========================== */

@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .card-3d, .card-hover-lift {
    transform: none !important;
    box-shadow: none !important;
  }
}

/* ===========================
   15. Accessibility
   =========================== */

/* Focus visible for keyboard navigation */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  left: -9999px;
  z-index: 999;
  padding: 1rem;
  background: var(--color-primary);
  color: white;
  text-decoration: none;
}

.skip-to-content:focus {
  left: 50%;
  transform: translateX(-50%);
  top: 1rem;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}