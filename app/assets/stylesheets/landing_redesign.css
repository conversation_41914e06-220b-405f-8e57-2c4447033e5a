/* Landing Page Redesign - Data Reflow Brand Colors */

/* ===========================
   Brand Color Variables
   =========================== */
:root {
  --brand-navy: #2d3748;
  --brand-navy-dark: #1a202c;
  --brand-navy-light: #4a5568;
  --brand-teal: #14b8a6;
  --brand-teal-light: #5eead4;
  --brand-teal-dark: #0d9488;
  --brand-gray-light: #f7fafc;
  --brand-gray-medium: #e2e8f0;
  --brand-text-light: rgba(255, 255, 255, 0.9);
  --brand-text-dark: #2d3748;
}

/* ===========================
   Landing Page Layout
   =========================== */

.landing-page {
  font-family: var(--font-family-base);
  color: var(--brand-text-dark);
  background: var(--brand-gray-light);
  line-height: var(--line-height-normal);
}

/* ===========================
   Hero Section
   =========================== */

.hero-section {
  background: var(--brand-navy);
  padding: var(--space-64) 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-24);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-64);
  align-items: center;
}

.hero-left {
  color: var(--brand-text-light);
}

.hero-title {
  font-size: clamp(48px, 8vw, 80px);
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: var(--space-24);
  color: var(--brand-text-light);
}

.hero-highlight {
  color: var(--brand-teal-light);
}

.hero-description {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  margin-bottom: var(--space-32);
  color: rgba(255, 255, 255, 0.8);
}

.hero-actions {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-48);
}

.hero-stats {
  display: flex;
  gap: var(--space-32);
}

.stat-item {
  text-align: left;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--brand-teal-light);
  margin-bottom: var(--space-4);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.7);
}

/* Hero Right Side */
.hero-right {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image {
  width: 100%;
  max-width: 500px;
}

.dashboard-mockup {
  background: var(--brand-gray-light);
  border-radius: var(--radius-lg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.mockup-header {
  background: var(--brand-gray-medium);
  padding: var(--space-12);
  display: flex;
  align-items: center;
}

.mockup-dots {
  display: flex;
  gap: var(--space-6);
}

.mockup-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--brand-teal);
}

.mockup-dots span:nth-child(2) {
  background: var(--brand-teal-light);
}

.mockup-dots span:nth-child(3) {
  background: var(--brand-navy-light);
}

.mockup-content {
  padding: var(--space-24);
  min-height: 300px;
}

.mockup-chart {
  height: 150px;
  background: linear-gradient(135deg, var(--brand-teal) 0%, var(--brand-teal-light) 100%);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-16);
  opacity: 0.8;
}

.mockup-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-12);
}

.mockup-metric {
  height: 60px;
  background: var(--brand-gray-medium);
  border-radius: var(--radius-sm);
}

/* Brand Section */
.brand-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-32);
}

.brand-logo {
  display: flex;
  height: 56px;
  width: 56px;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--brand-teal) 0%, var(--brand-teal-light) 100%);
  box-shadow: 0 10px 25px rgba(49, 151, 149, 0.4);
  margin-right: var(--space-16);
}

.brand-icon {
  height: 32px;
  width: 32px;
  color: white;
}

.brand-name {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--brand-text-light);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-12) var(--space-20);
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(49, 151, 149, 0.3);
  box-shadow: 0 8px 32px rgba(49, 151, 149, 0.2);
  margin-bottom: var(--space-24);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--brand-teal);
  border-radius: var(--radius-full);
  margin-right: var(--space-8);
  box-shadow: 0 0 8px rgba(49, 151, 149, 0.6);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--brand-teal-dark);
}

.status-icon {
  margin-left: var(--space-8);
  height: 16px;
  width: 16px;
  color: var(--brand-teal);
}

/* Headlines */
.hero-headline {
  font-size: clamp(var(--font-size-4xl), 8vw, 72px);
  font-weight: var(--font-weight-bold);
  color: var(--brand-text-light);
  margin-bottom: var(--space-24);
  line-height: var(--line-height-tight);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-highlight {
  background: linear-gradient(135deg, var(--brand-teal-light) 0%, var(--brand-teal) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  text-shadow: none;
}

.hero-subheadline {
  font-size: var(--font-size-xl);
  color: var(--brand-text-light);
  margin-bottom: var(--space-40);
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--line-height-normal);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ===========================
   Dashboard Preview
   =========================== */

.dashboard-preview {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-48);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 1200px;
  margin: 0 auto var(--space-48) auto;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-24);
}

.dashboard-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.dashboard-status {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.dashboard-status .status-indicator {
  margin-left: var(--space-8);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-24);
}

.metric-card {
  text-align: center;
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-card--revenue {
  background: linear-gradient(135deg, rgba(49, 151, 149, 0.15) 0%, rgba(44, 122, 123, 0.15) 100%);
  border-color: rgba(49, 151, 149, 0.4);
  box-shadow: 0 4px 20px rgba(49, 151, 149, 0.2);
}

.metric-card--orders {
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.15) 0%, rgba(74, 85, 104, 0.15) 100%);
  border-color: rgba(45, 55, 72, 0.4);
  box-shadow: 0 4px 20px rgba(45, 55, 72, 0.2);
}

.metric-card--customers {
  background: linear-gradient(135deg, rgba(79, 209, 199, 0.15) 0%, rgba(49, 151, 149, 0.15) 100%);
  border-color: rgba(79, 209, 199, 0.4);
  box-shadow: 0 4px 20px rgba(79, 209, 199, 0.2);
}

.metric-card--conversion {
  background: linear-gradient(135deg, rgba(44, 122, 123, 0.15) 0%, rgba(26, 32, 44, 0.15) 100%);
  border-color: rgba(44, 122, 123, 0.4);
  box-shadow: 0 4px 20px rgba(44, 122, 123, 0.2);
}

.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-8);
}

.metric-card--revenue .metric-value { color: var(--brand-teal); }
.metric-card--orders .metric-value { color: var(--brand-navy); }
.metric-card--customers .metric-value { color: var(--brand-teal-light); }
.metric-card--conversion .metric-value { color: var(--brand-teal-dark); }

.metric-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-4);
}

.metric-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.metric-change--positive {
  color: var(--brand-teal);
}

/* AI Insight Card */
.ai-insight-card {
  background: linear-gradient(135deg, rgba(49, 151, 149, 0.2) 0%, rgba(79, 209, 199, 0.2) 100%);
  border: 1px solid rgba(49, 151, 149, 0.4);
  border-radius: var(--radius-lg);
  padding: var(--space-16);
  box-shadow: 0 4px 20px rgba(49, 151, 149, 0.15);
}

.ai-insight-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-12);
}

.ai-insight-icon {
  height: 20px;
  width: 20px;
  color: var(--brand-teal);
  margin-right: var(--space-8);
}

.ai-insight-label {
  font-weight: var(--font-weight-medium);
  color: var(--brand-teal-dark);
}

.ai-insight-confidence {
  margin-left: auto;
  font-size: var(--font-size-xs);
  color: var(--brand-teal);
}

.ai-insight-text {
  color: var(--brand-teal-dark);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* ===========================
   CTA Section
   =========================== */

.cta-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  justify-content: center;
  align-items: center;
  margin-bottom: var(--space-40);
}

@media (min-width: 640px) {
  .cta-section {
    flex-direction: row;
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-24);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-standard);
  text-decoration: none;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn--large {
  padding: var(--space-16) var(--space-32);
  font-size: var(--font-size-lg);
}

.btn--primary {
  background: linear-gradient(135deg, var(--brand-teal) 0%, var(--brand-teal-dark) 100%);
  color: white;
  box-shadow: 0 10px 25px rgba(49, 151, 149, 0.4);
}

.btn--primary:hover {
  background: linear-gradient(135deg, var(--brand-teal-dark) 0%, var(--brand-navy-light) 100%);
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(49, 151, 149, 0.5);
}

.btn--outline {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid rgba(49, 151, 149, 0.3);
  color: var(--brand-teal-dark);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.btn--outline:hover {
  background: white;
  border-color: rgba(49, 151, 149, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.btn-icon {
  height: 20px;
  width: 20px;
  margin-left: var(--space-8);
}

.btn-icon--left {
  margin-left: 0;
  margin-right: var(--space-8);
}

.btn-subtitle {
  margin-left: var(--space-8);
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

/* ===========================
   Trust Indicators
   =========================== */

.trust-indicators {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: var(--space-32);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.trust-indicator {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-md);
  border: 1px solid rgba(49, 151, 149, 0.2);
  box-shadow: 0 4px 15px rgba(49, 151, 149, 0.1);
  transition: all var(--duration-normal) var(--ease-standard);
}

.trust-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(49, 151, 149, 0.15);
}

.trust-icon {
  height: 16px;
  width: 16px;
  color: var(--brand-teal);
  margin-right: var(--space-8);
}

/* ===========================
   Section Layouts
   =========================== */

.section-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-24);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-48);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-32);
}

/* ===========================
   Trusted By Section
   =========================== */

.trusted-by-section {
  padding: var(--space-64) 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.trusted-brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-32);
  align-items: center;
  justify-items: center;
}

.trusted-brand {
  color: #475569;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  opacity: 0.8;
  transition: all var(--duration-normal) var(--ease-standard);
}

.trusted-brand:hover {
  opacity: 1;
  color: #334155;
  transform: translateY(-2px);
}

/* ===========================
   Stats Section
   =========================== */

.stats-section {
  padding: var(--space-64) 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-32);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: clamp(var(--font-size-3xl), 5vw, var(--font-size-4xl));
  font-weight: var(--font-weight-bold);
  color: var(--brand-navy);
  margin-bottom: var(--space-8);
  background: linear-gradient(135deg, var(--brand-teal) 0%, var(--brand-teal-light) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  color: var(--brand-navy-light);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* ===========================
   Glass Morphism Effects
   =========================== */

.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

/* ===========================
   Responsive Design
   =========================== */

@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-32);
    text-align: center;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-24);
  }

  .dashboard-preview {
    padding: var(--space-24);
    margin-bottom: var(--space-32);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-16);
  }

  .trusted-brands-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-16);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-24);
  }

  .trust-indicators {
    flex-direction: column;
    gap: var(--space-16);
  }
}

@media (max-width: 480px) {
  .brand-section {
    flex-direction: column;
    gap: var(--space-16);
  }

  .brand-logo {
    margin-right: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .trusted-brands-grid {
    grid-template-columns: 1fr;
  }
}

/* ===========================
   Accessibility
   =========================== */

@media (prefers-reduced-motion: reduce) {
  .btn,
  .metric-card,
  .trusted-brand {
    transition: none;
  }

  .btn:hover,
  .metric-card:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glass-card,
  .dashboard-preview,
  .status-badge,
  .btn--outline {
    border-width: 2px;
    border-color: var(--color-text);
  }

  .btn--primary {
    background: var(--color-text);
    color: var(--color-surface);
  }
}
