
:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(20, 184, 166, 1);
  --color-primary-hover: rgba(13, 148, 136, 1);
  --color-primary-active: rgba(15, 118, 110, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(20, 184, 166, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Professional enterprise-grade styles */
.header {
  background: #1e293b;
  color: #ffffff;
  padding: var(--space-16) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-md);
}

.header--scrolled {
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(10px);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav__brand {
  display: flex;
  align-items: center;
}

.nav__logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: #ffffff;
  margin: 0;
}

.nav__menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-32);
}

.nav__link {
  color: #e2e8f0;
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-fast) var(--ease-standard);
}

.nav__link:hover,
.nav__link--active {
  color: #ffffff;
}

.nav__actions {
  display: flex;
  gap: var(--space-16);
}

.nav__toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-8);
}

.nav__toggle-bar {
  width: 25px;
  height: 3px;
  background: #ffffff;
  margin: 3px 0;
  transition: var(--duration-fast);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: #ffffff;
  padding: var(--space-32) 0;
  min-height: 600px;
  display: flex;
  align-items: center;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-16);
}

.hero__highlight {
  color: #32b8c6;
}

.hero__subtitle {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-24);
  color: #e2e8f0;
}

.hero__actions {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.hero__metrics {
  display: flex;
  gap: var(--space-24);
}

.metric {
  text-align: center;
}

.metric__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: #32b8c6;
  margin-bottom: var(--space-4);
}

.metric__label {
  font-size: var(--font-size-sm);
  color: #cbd5e1;
}

.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero__img {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: block;
}

/* Section Headers */
.section__header {
  text-align: center;
  margin-bottom: var(--space-32);
}

.section__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: #1e293b;
  margin-bottom: var(--space-16);
}

.section__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Testimonials */
.testimonials {
  padding: var(--space-32) 0;
  background: var(--color-background);
}

.testimonials__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-24);
}

.testimonial {
  padding: var(--space-24);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.testimonial:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.testimonial__content {
  margin-bottom: var(--space-20);
}

.testimonial__quote {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  margin-bottom: var(--space-16);
  font-style: italic;
}

.testimonial__metrics {
  display: flex;
  gap: var(--space-8);
  flex-wrap: wrap;
}

.testimonial__author {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.testimonial__avatar {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  object-fit: cover;
  display: block;
  flex-shrink: 0;
}

.testimonial__info {
  flex: 1;
}

.testimonial__name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.testimonial__title {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.testimonial__company {
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

/* ROI Section */
.roi-section {
  padding: var(--space-32) 0;
  background: #1e293b;
  color: #ffffff;
}

.roi__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.roi__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-16);
  color: #ffffff;
}

.roi__description {
  font-size: var(--font-size-lg);
  color: #e2e8f0;
  margin-bottom: var(--space-24);
}

.roi__stats {
  display: flex;
  gap: var(--space-24);
}

.roi__stat {
  text-align: center;
}

.roi__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: #32b8c6;
  margin-bottom: var(--space-4);
}

.roi__label {
  font-size: var(--font-size-sm);
  color: #cbd5e1;
}

.roi__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.roi__img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: block;
}

/* Dashboard Showcase */
.dashboard-showcase {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.dashboard__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
}

.dashboard__item {
  background: var(--color-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.dashboard__item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.dashboard__image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.dashboard__content {
  padding: var(--space-20);
}

.dashboard__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.dashboard__description {
  color: var(--color-text-secondary);
}

/* Features */
.features {
  padding: var(--space-32) 0;
  background: var(--color-background);
}

.features__grid {
  display: grid;
  gap: var(--space-32);
}

.feature {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-24);
  align-items: center;
}

.feature:nth-child(even) {
  direction: rtl;
}

.feature:nth-child(even) .feature__content {
  direction: ltr;
}

.feature__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature__img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  display: block;
}

.feature__content {
  padding: var(--space-16);
}

.feature__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-12);
}

.feature__description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-16);
}

.feature__benefit {
  display: flex;
  align-items: center;
}

/* Happy Customers */
.happy-customers {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.happy-customers__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.happy-customers__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.happy-customers__description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.happy-customers__images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
}

.happy-customers__img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  display: block;
}

/* Pricing */
.pricing {
  padding: var(--space-32) 0;
  background: var(--color-background);
}

.pricing__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
}

.pricing__card {
  padding: var(--space-24);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.pricing__card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.pricing__card--popular {
  border: 2px solid var(--color-primary);
  transform: scale(1.05);
}

.pricing__badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.pricing__header {
  text-align: center;
  margin-bottom: var(--space-24);
}

.pricing__plan {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.pricing__price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: var(--space-8);
}

.pricing__currency {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.pricing__amount {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 var(--space-4);
}

.pricing__period {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.pricing__target {
  color: var(--color-text-secondary);
  font-style: italic;
}

.pricing__features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-24) 0;
}

.pricing__features li {
  padding: var(--space-8) 0;
  border-bottom: 1px solid var(--color-border);
}

.pricing__features li:last-child {
  border-bottom: none;
}

.pricing__features li::before {
  content: "✓";
  color: var(--color-success);
  font-weight: var(--font-weight-bold);
  margin-right: var(--space-8);
}

/* CTA */
.cta {
  padding: var(--space-32) 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: #ffffff;
  text-align: center;
}

.cta__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-16);
}

.cta__description {
  font-size: var(--font-size-lg);
  color: #e2e8f0;
  margin-bottom: var(--space-24);
}

.cta__actions {
  display: flex;
  gap: var(--space-16);
  justify-content: center;
}

/* Footer */
.footer {
  background: #1e293b;
  color: #ffffff;
  padding: var(--space-32) 0 var(--space-16);
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: var(--space-32);
  margin-bottom: var(--space-24);
}

.footer__logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: #ffffff;
  margin-bottom: var(--space-8);
}

.footer__description {
  color: #e2e8f0;
  margin: 0;
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-24);
}

.footer__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: #ffffff;
  margin-bottom: var(--space-16);
}

.footer__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__list li {
  margin-bottom: var(--space-8);
}

.footer__link {
  color: #e2e8f0;
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer__link:hover {
  color: #ffffff;
}

.footer__bottom {
  text-align: center;
  padding-top: var(--space-16);
  border-top: 1px solid #334155;
  color: #cbd5e1;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-20);
}

.modal {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-24) var(--space-24) var(--space-16);
  border-bottom: 1px solid var(--color-border);
}

.modal__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
}

.modal__close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: var(--space-4);
}

.modal__close:hover {
  color: var(--color-text);
}

.modal__content {
  padding: var(--space-24);
}

.trial-form,
.demo-form {
  max-width: 400px;
  margin: 0 auto;
}

.trial-form p,
.demo-form p {
  text-align: center;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-24);
}

/* Success Message */
.success-message {
  position: fixed;
  top: var(--space-20);
  right: var(--space-20);
  background: var(--color-success);
  color: var(--color-surface);
  padding: var(--space-16) var(--space-20);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  cursor: pointer;
  animation: slideIn 0.3s ease-out;
}

.success-message__content {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.success-message__icon {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation classes */
.animate-in {
  animation: fadeInUp var(--duration-normal) var(--ease-standard);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav__menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: #1e293b;
    flex-direction: column;
    padding: var(--space-20);
    gap: var(--space-16);
    transform: translateY(-100%);
    transition: transform var(--duration-normal) var(--ease-standard);
    box-shadow: var(--shadow-lg);
  }

  .nav__menu--open {
    transform: translateY(0);
  }

  .nav__toggle {
    display: flex;
  }

  .nav__toggle--open .nav__toggle-bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .nav__toggle--open .nav__toggle-bar:nth-child(2) {
    opacity: 0;
  }

  .nav__toggle--open .nav__toggle-bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .nav__actions {
    display: none;
  }

  .hero__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero__metrics {
    justify-content: center;
  }

  .roi__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .roi__stats {
    justify-content: center;
  }

  .feature {
    grid-template-columns: 1fr;
  }

  .feature:nth-child(even) {
    direction: ltr;
  }

  .happy-customers__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .pricing__grid {
    grid-template-columns: 1fr;
  }

  .pricing__card--popular {
    transform: none;
  }

  .footer__content {
    grid-template-columns: 1fr;
  }

  .footer__links {
    grid-template-columns: 1fr;
  }

  .cta__actions {
    flex-direction: column;
    align-items: center;
  }
}

/* Button overrides for dark theme */
.btn--outline {
  border: 1px solid #ffffff;
  color: #ffffff;
}

.btn--outline:hover {
  background: #ffffff;
  color: #1e293b;
}

.hero .btn--outline {
  border: 1px solid #32b8c6;
  color: #32b8c6;
}

.hero .btn--outline:hover {
  background: #32b8c6;
  color: #1e293b;
}

.cta .btn--outline {
  border: 1px solid #ffffff;
  color: #ffffff;
}

.cta .btn--outline:hover {
  background: #ffffff;
  color: #1e293b;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.testimonial,
.feature,
.dashboard__item {
  animation: fadeInUp var(--duration-normal) var(--ease-standard);
}

/* Additional status styles for enterprise look */
.status--success {
  background-color: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

/* Enhanced professional spacing */
section {
  padding: var(--space-32) 0;
}

/* Professional hover effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Enhanced typography hierarchy */
.section__title {
  letter-spacing: -0.025em;
}

.hero__title {
  letter-spacing: -0.025em;
}

/* Professional focus states */
.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(50, 184, 198, 0.4);
}

/* Ensure proper image loading */
img {
  max-width: 100%;
  height: auto;
  display: block;
  border: 0;
}

/* Image fallback */
img::before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  background: var(--color-border);
}

/* Ensure images are properly loaded */
img[src] {
  opacity: 1;
  transition: opacity var(--duration-fast) var(--ease-standard);
}

img:not([src]) {
  opacity: 0;
}