# Data Reflow Design System

## Table of Contents
1. [Design Principles & Philosophy](#design-principles--philosophy)
2. [Color System](#color-system)
3. [Typography System](#typography-system)
4. [Spacing & Layout System](#spacing--layout-system)
5. [Component Library](#component-library)
6. [Visual Effects & Interactions](#visual-effects--interactions)
7. [Usage Guidelines](#usage-guidelines)

---

## Design Principles & Philosophy

### Enterprise-Grade Aesthetic
Our design system prioritizes **professional, sophisticated, and trustworthy** visual communication that builds credibility with enterprise customers.

**Core Principles:**
- **Clarity over Decoration**: Every visual element serves a functional purpose
- **Consistency over Creativity**: Predictable patterns reduce cognitive load
- **Accessibility First**: Inclusive design for all users and abilities
- **Performance Conscious**: Efficient CSS and optimized user experience

### Glass Morphism Philosophy
We employ **subtle glass morphism effects** to create depth and premium feel without overwhelming the interface:
- Semi-transparent backgrounds with backdrop blur
- Subtle borders and inset highlights
- Layered shadows for depth perception
- Maintains readability and accessibility

### User Experience Approach
- **Mobile-First**: Responsive design starting from smallest screens
- **Progressive Enhancement**: Core functionality works without JavaScript
- **Intuitive Navigation**: Clear information hierarchy and user flows
- **Reduced Friction**: Streamlined interactions and minimal cognitive load

---

## Color System

### CSS Custom Properties
Our color system uses CSS custom properties for consistency and maintainability:

```css
:root {
  /* Primary Brand Colors */
  --color-primary: #21808d;
  --color-primary-hover: #1a6b75;
  --color-primary-rgb: 33, 128, 141;

  /* Surface & Background */
  --color-background: #f8fafc;
  --color-surface: #ffffff;
  --color-surface-rgb: 255, 255, 253;

  /* Text Hierarchy */
  --color-text: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;

  /* Borders & Dividers */
  --color-border: #e2e8f0;
  --color-border-rgb: 226, 232, 240;

  /* Status Colors */
  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Button Colors */
  --color-btn-primary-text: #ffffff;
}
```

### Hero Background Gradient
**Primary gradient used across hero sections:**
```css
background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
```

### Color Usage Guidelines

#### Primary Colors
- **--color-primary**: Main brand color for CTAs, links, and key UI elements
- **--color-primary-hover**: Hover states for primary elements
- Use sparingly to maintain impact and hierarchy

#### Text Colors
- **--color-text**: Primary text, headings, important content
- **--color-text-secondary**: Secondary text, descriptions, labels
- **--color-text-tertiary**: Placeholder text, disabled states, subtle hints

#### Surface Colors
- **--color-background**: Page backgrounds, main content areas
- **--color-surface**: Card backgrounds, elevated content
- Always ensure sufficient contrast with text colors

---

## Typography System

### Font Size Scale
```css
:root {
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
}
```

### Font Weight Hierarchy
```css
:root {
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}
```

### Line Height Standards
```css
:root {
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
}
```

### Gradient Text Effects
For premium headings and emphasis:
```css
.gradient-text {
  background: linear-gradient(135deg, var(--color-text), var(--color-text-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
```

### Typography Usage Guidelines

#### Headings
- **H1**: `--font-size-4xl` or `--font-size-5xl` with `--font-weight-bold`
- **H2**: `--font-size-3xl` with `--font-weight-bold`
- **H3**: `--font-size-2xl` with `--font-weight-bold`
- **H4**: `--font-size-xl` with `--font-weight-semibold`

#### Body Text
- **Primary**: `--font-size-base` with `--font-weight-normal`
- **Large**: `--font-size-lg` with `--font-weight-medium`
- **Small**: `--font-size-sm` with `--font-weight-medium`

#### Labels & UI Text
- **Form Labels**: `--font-size-sm` with `--font-weight-medium`
- **Button Text**: `--font-size-base` with `--font-weight-semibold`
- **Captions**: `--font-size-xs` with `--font-weight-medium`

---

## Spacing & Layout System

### Spacing Scale
```css
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-4: 1rem;      /* 16px */
  --space-8: 2rem;      /* 32px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */
  --space-40: 10rem;    /* 160px */
  --space-48: 12rem;    /* 192px */
  --space-56: 14rem;    /* 224px */
  --space-64: 16rem;    /* 256px */
}
```

### Container & Grid System
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Responsive Grid */
.grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-8); }
.grid-3 { display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space-8); }
.grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: var(--space-8); }

/* Auto-fit responsive grids */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-8);
}
```

### Responsive Breakpoints
```css
/* Mobile First Approach */
@media (max-width: 768px) { /* Mobile */ }
@media (max-width: 1024px) { /* Tablet */ }
@media (min-width: 1025px) { /* Desktop */ }
```

### Layout Patterns

#### Section Spacing
- **Section Padding**: `var(--space-16)` to `var(--space-20)` vertical
- **Component Spacing**: `var(--space-8)` to `var(--space-12)` between components
- **Element Spacing**: `var(--space-4)` to `var(--space-6)` between related elements

#### Content Width
- **Reading Width**: Max 65-75 characters per line
- **Form Width**: Max 480px for optimal usability
- **Card Width**: 280px-400px for optimal content display

---

## Component Library

### Button System

#### Primary Button
```css
.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-weight: var(--font-weight-semibold);
  transition: all var(--duration-fast) var(--ease-standard);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
}
```

#### Outline Button
```css
.btn--outline {
  background: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-weight: var(--font-weight-semibold);
  transition: all var(--duration-fast) var(--ease-standard);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}
```

#### Button Sizes
```css
.btn--sm { padding: var(--space-2) var(--space-4); font-size: var(--font-size-sm); }
.btn--lg { padding: var(--space-4) var(--space-8); font-size: var(--font-size-lg); }
```

### Form Components

#### Input Fields
```css
.auth-page__input {
  width: 100%;
  padding: var(--space-4) var(--space-5) var(--space-4) var(--space-12);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

.auth-page__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}
```

#### Labels
```css
.auth-page__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-2);
  display: block;
}
```

### Card Components

#### Glass Morphism Card
```css
.glass-card {
  background: rgba(var(--color-surface-rgb), 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(var(--color-border-rgb), 0.3);
  border-radius: var(--radius-2xl);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
}
```

#### Premium Card with Hover
```css
.premium-card {
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--ease-standard);
  border: 1px solid var(--color-border);
}

.premium-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

### Navigation Components

#### Navbar Pattern
```css
.navbar {
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(var(--color-border-rgb), 0.3);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  padding: var(--space-4) 0;
}

.navbar__link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.navbar__link:hover {
  color: var(--color-primary);
}
```

#### Mobile Navigation

```css
.navbar__mobile-menu {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-6);
  margin-top: var(--space-4);
}
```

### Icon System

#### Icon Sizing Standards

```css
.icon--xs { width: 12px; height: 12px; }
.icon--sm { width: 16px; height: 16px; }
.icon--md { width: 20px; height: 20px; }
.icon--lg { width: 24px; height: 24px; }
.icon--xl { width: 32px; height: 32px; }
```

#### Icon Containers
```css
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1), rgba(var(--color-primary-rgb), 0.05));
}

.icon-container--primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: white;
}
```

---

## Visual Effects & Interactions

### Shadow System
```css
:root {
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
}
```

### Border Radius System
```css
:root {
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;  /* Fully rounded */
}
```

### Animation & Timing
```css
:root {
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --ease-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
}
```

### Glass Morphism Implementation
```css
.glass-effect {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
}

/* Dark background variant */
.glass-effect--dark {
  background: rgba(var(--color-surface-rgb), 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(var(--color-border-rgb), 0.3);
}
```

### Hover & Focus States

#### Standard Hover Pattern
```css
.interactive-element {
  transition: all var(--duration-normal) var(--ease-standard);
}

.interactive-element:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
```

#### Focus States
```css
.focusable:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
  border-color: var(--color-primary);
}
```

### Gradient Effects

#### Background Gradients
```css
/* Hero gradient */
.gradient-hero {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* Subtle accent gradients */
.gradient-accent {
  background:
    radial-gradient(circle at 30% 40%, rgba(50, 184, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(50, 184, 198, 0.1) 0%, transparent 50%);
}
```

#### Text Gradients
```css
.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-brand {
  background: linear-gradient(135deg, #32b8c6 0%, #21808d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
```

---

## Usage Guidelines

### Component Selection Guide

#### When to Use Primary Buttons
- **Primary actions**: Sign up, submit forms, confirm actions
- **Call-to-actions**: "Start Free Trial", "Get Started"
- **Limit**: Maximum 1-2 per page to maintain hierarchy

#### When to Use Outline Buttons
- **Secondary actions**: "Learn More", "Cancel", "Back"
- **Alternative options**: When primary action is already present
- **Navigation**: Links that look like buttons but aren't primary

#### When to Use Glass Morphism
- **Authentication pages**: Login, signup, password reset forms
- **Modal overlays**: Dialogs, popups, temporary content
- **Feature highlights**: Premium content, special sections
- **Avoid**: Main content areas, text-heavy sections

### Do's and Don'ts

#### ✅ Do's
- **Use CSS custom properties** for all colors, spacing, and typography
- **Maintain consistent spacing** using the spacing scale
- **Apply hover states** to all interactive elements
- **Use semantic HTML** with proper ARIA labels
- **Test on mobile devices** and ensure touch-friendly targets
- **Maintain color contrast** ratios for accessibility
- **Use the established button classes** rather than custom styling

#### ❌ Don'ts
- **Don't use hardcoded colors** - always use CSS custom properties
- **Don't mix spacing scales** - stick to the established system
- **Don't overuse glass morphism** - reserve for special elements
- **Don't ignore focus states** - ensure keyboard accessibility
- **Don't use too many font weights** - stick to the hierarchy
- **Don't create new button styles** without updating the system
- **Don't use animations longer than 350ms** for UI interactions

### Extending the System

#### Adding New Colors
1. Add to CSS custom properties in `:root`
2. Include RGB variants for transparency: `--color-new-rgb: r, g, b`
3. Define hover states: `--color-new-hover`
4. Document usage guidelines
5. Test accessibility contrast ratios

#### Adding New Components
1. Follow established naming conventions: `component__element--modifier`
2. Use existing design tokens (colors, spacing, typography)
3. Include hover and focus states
4. Add responsive behavior
5. Document in this guide with examples

#### Responsive Considerations
1. **Mobile First**: Start with mobile styles, enhance for larger screens
2. **Touch Targets**: Minimum 44px for interactive elements
3. **Readable Text**: Minimum 16px font size on mobile
4. **Spacing**: Reduce spacing on smaller screens appropriately
5. **Navigation**: Ensure mobile navigation is accessible and usable

### Implementation Examples

#### Proper Form Implementation
```html
<div class="auth-page__field">
  <label class="auth-page__label" for="email">Email Address</label>
  <div class="auth-page__input-wrapper">
    <div class="auth-page__input-icon">
      <svg><!-- email icon --></svg>
    </div>
    <input
      type="email"
      id="email"
      class="auth-page__input"
      placeholder="<EMAIL>"
      required
    >
  </div>
</div>
```

#### Proper Card Implementation
```html
<div class="premium-card">
  <div class="card__header">
    <h3 class="card__title">Feature Title</h3>
  </div>
  <div class="card__content">
    <p class="card__description">Feature description...</p>
  </div>
  <div class="card__actions">
    <button class="btn btn--primary">Primary Action</button>
    <button class="btn btn--outline">Secondary Action</button>
  </div>
</div>
```

### Accessibility Guidelines

#### Color & Contrast
- **Text on background**: Minimum 4.5:1 contrast ratio
- **Large text**: Minimum 3:1 contrast ratio
- **Interactive elements**: Clear visual distinction
- **Don't rely on color alone** for important information

#### Keyboard Navigation
- **Tab order**: Logical and predictable
- **Focus indicators**: Visible and consistent
- **Skip links**: For main content navigation
- **Escape key**: Closes modals and dropdowns

#### Screen Readers
- **Semantic HTML**: Use proper heading hierarchy
- **Alt text**: Descriptive for images and icons
- **ARIA labels**: For complex interactions
- **Form labels**: Properly associated with inputs

---

## Conclusion

This design system provides the foundation for maintaining consistency and quality across the Data Reflow platform. By following these guidelines and using the established patterns, we ensure a cohesive, professional, and accessible user experience that builds trust with our enterprise customers.

For questions or suggestions about extending this design system, please refer to the development team or create an issue in the project repository.
```
