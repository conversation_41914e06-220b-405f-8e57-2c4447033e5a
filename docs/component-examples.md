# Data Reflow Design System - Component Examples

This document provides practical examples of how to implement components using our design system tokens and patterns.

## Button Components

### Primary Button

```html
<button class="btn btn--primary">
  Get Started
</button>

<button class="btn btn--primary btn--lg">
  Start Free Trial
</button>
```

```css
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--ease-standard);
  cursor: pointer;
  text-decoration: none;
  border: 1px solid transparent;
  padding: var(--space-3) var(--space-6);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn--lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}
```

### Outline Button

```html
<button class="btn btn--outline">
  Learn More
</button>
```

```css
.btn--outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}
```

## Form Components

### Input Field with Icon

```html
<div class="form-field">
  <label class="form-label" for="email">Email Address</label>
  <div class="input-wrapper">
    <div class="input-icon">
      <svg class="icon icon--sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
      </svg>
    </div>
    <input 
      type="email" 
      id="email" 
      class="form-input form-input--with-icon"
      placeholder="<EMAIL>"
      required
    >
  </div>
</div>
```

```css
.form-field {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-2);
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-tertiary);
  pointer-events: none;
}

.form-input {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

.form-input--with-icon {
  padding-left: var(--space-12);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 var(--focus-ring-width) var(--focus-ring-color);
}
```

## Card Components

### Premium Card

```html
<div class="card card--premium">
  <div class="card__header">
    <div class="card__icon">
      <svg class="icon icon--lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M13 10V3L4 14h7v7l9-11h-7z"/>
      </svg>
    </div>
    <h3 class="card__title">Real-time Analytics</h3>
  </div>
  <div class="card__content">
    <p class="card__description">
      Get instant insights from your data with our real-time analytics engine.
    </p>
  </div>
  <div class="card__footer">
    <button class="btn btn--primary btn--sm">Learn More</button>
  </div>
</div>
```

```css
.card {
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-border);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.card--premium {
  box-shadow: var(--shadow-sm);
}

.card--premium:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(var(--color-primary-rgb), 0.2);
}

.card__header {
  padding: var(--space-6) var(--space-6) var(--space-4);
}

.card__icon {
  width: var(--space-12);
  height: var(--space-12);
  background: linear-gradient(135deg, 
    rgba(var(--color-primary-rgb), 0.1), 
    rgba(var(--color-primary-rgb), 0.05));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.card__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0;
}

.card__content {
  padding: 0 var(--space-6) var(--space-4);
}

.card__description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.card__footer {
  padding: var(--space-4) var(--space-6) var(--space-6);
}
```

### Glass Morphism Card

```html
<div class="card card--glass">
  <div class="card__content">
    <h3 class="card__title">Premium Feature</h3>
    <p class="card__description">
      Experience the power of our advanced analytics platform.
    </p>
  </div>
</div>
```

```css
.card--glass {
  background: var(--glass-background);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-glass);
}
```

## Navigation Components

### Navbar

```html
<nav class="navbar">
  <div class="navbar__container">
    <div class="navbar__brand">
      <img src="/logo.svg" alt="Data Reflow" class="navbar__logo">
    </div>
    <div class="navbar__nav">
      <a href="/features" class="navbar__link">Features</a>
      <a href="/pricing" class="navbar__link">Pricing</a>
      <a href="/about" class="navbar__link">About</a>
    </div>
    <div class="navbar__actions">
      <a href="/login" class="btn btn--outline btn--sm">Sign In</a>
      <a href="/signup" class="btn btn--primary btn--sm">Get Started</a>
    </div>
  </div>
</nav>
```

```css
.navbar {
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: var(--glass-blur);
  border-bottom: 1px solid var(--glass-border);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  padding: var(--space-4) 0;
}

.navbar__container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar__brand {
  display: flex;
  align-items: center;
}

.navbar__logo {
  height: 2rem;
  width: auto;
}

.navbar__nav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.navbar__link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.navbar__link:hover {
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}
```

## Layout Components

### Container

```html
<div class="container">
  <div class="container__content">
    <!-- Your content here -->
  </div>
</div>
```

```css
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }
}
```

### Grid System

```html
<div class="grid grid--2">
  <div class="grid__item">Item 1</div>
  <div class="grid__item">Item 2</div>
</div>

<div class="grid grid--auto-fit">
  <div class="grid__item">Item 1</div>
  <div class="grid__item">Item 2</div>
  <div class="grid__item">Item 3</div>
</div>
```

```css
.grid {
  display: grid;
  gap: var(--space-8);
}

.grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid--auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (max-width: 768px) {
  .grid--2,
  .grid--3,
  .grid--4 {
    grid-template-columns: 1fr;
  }
}
```

## Icon Components

### Icon with Container

```html
<div class="icon-container icon-container--primary">
  <svg class="icon icon--md" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
          d="M13 10V3L4 14h7v7l9-11h-7z"/>
  </svg>
</div>
```

```css
.icon {
  width: var(--icon-size-md);
  height: var(--icon-size-md);
  flex-shrink: 0;
}

.icon--sm { width: var(--icon-size-sm); height: var(--icon-size-sm); }
.icon--lg { width: var(--icon-size-lg); height: var(--icon-size-lg); }
.icon--xl { width: var(--icon-size-xl); height: var(--icon-size-xl); }

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  padding: var(--space-3);
}

.icon-container--primary {
  background: var(--gradient-primary);
  color: var(--color-btn-primary-text);
}

.icon-container--subtle {
  background: linear-gradient(135deg, 
    rgba(var(--color-primary-rgb), 0.1), 
    rgba(var(--color-primary-rgb), 0.05));
  color: var(--color-primary);
}
```

## Utility Classes

### Text Utilities

```css
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }
```

### Spacing Utilities

```css
.mt-4 { margin-top: var(--space-4); }
.mb-4 { margin-bottom: var(--space-4); }
.ml-4 { margin-left: var(--space-4); }
.mr-4 { margin-right: var(--space-4); }

.pt-4 { padding-top: var(--space-4); }
.pb-4 { padding-bottom: var(--space-4); }
.pl-4 { padding-left: var(--space-4); }
.pr-4 { padding-right: var(--space-4); }
```

### Display Utilities

```css
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
```
