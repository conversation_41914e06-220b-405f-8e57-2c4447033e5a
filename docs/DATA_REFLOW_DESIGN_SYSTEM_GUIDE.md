# Data Reflow Design System Guide

**The Definitive Reference for Enterprise-Grade Design Consistency**

---

## Table of Contents

1. [Design Principles & Philosophy](#design-principles--philosophy)
2. [Color System](#color-system)
3. [Typography System](#typography-system)
4. [Spacing & Layout System](#spacing--layout-system)
5. [Component Library](#component-library)
6. [Visual Effects & Interactions](#visual-effects--interactions)
7. [Implementation Guidelines](#implementation-guidelines)

---

## Design Principles & Philosophy

### Enterprise-Grade Aesthetic

Our design system embodies **professional sophistication** that builds trust with enterprise customers while remaining accessible to SMEs.

**Core Design Principles:**

- **Clarity over Decoration**: Every visual element serves a functional purpose
- **Consistency over Creativity**: Predictable patterns reduce cognitive load
- **Accessibility First**: WCAG 2.1 AA compliance with minimum 4.5:1 contrast ratios
- **Performance Conscious**: Efficient CSS with minimal visual complexity

### Glass Morphism Philosophy

We implement **subtle glass morphism effects** to create depth and premium feel:

```css
/* Glass Morphism Implementation */
background: rgba(var(--color-surface-rgb, 255, 255, 253), 0.95);
backdrop-filter: blur(20px);
border: 1px solid rgba(var(--color-border-rgb, 226, 232, 240), 0.3);
box-shadow: 
  0 8px 32px rgba(0, 0, 0, 0.08),
  0 1px 0 rgba(255, 255, 255, 0.5) inset;
```

**Technical Specifications:**
- **Backdrop Blur**: `blur(20px)` for optimal readability
- **Background Opacity**: 0.8-0.95 for content visibility
- **Border Opacity**: 0.3 for subtle definition
- **Inset Highlight**: `rgba(255, 255, 255, 0.5)` for premium depth

### Accessibility Standards

- **Color Contrast**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Focus States**: 3px focus ring with `rgba(33, 128, 141, 0.4)` color
- **Keyboard Navigation**: Logical tab order with visible focus indicators
- **Touch Targets**: Minimum 44px for interactive elements

### Mobile-First Responsive Design

```css
/* Breakpoint Strategy */
/* Base: Mobile (320px+) */
@media (max-width: 768px) { /* Mobile optimizations */ }
@media (max-width: 1024px) { /* Tablet adjustments */ }
@media (min-width: 1025px) { /* Desktop enhancements */ }
```

---

## Color System

### Primary Brand Colors

```css
:root {
  /* Primary Brand Palette - Teal */
  --color-primary: rgba(20, 184, 166, 1);        /* #14b8a6 */
  --color-primary-hover: rgba(13, 148, 136, 1);  /* #0d9488 */
  --color-primary-active: rgba(15, 118, 110, 1); /* #0f766e */
  --color-primary-rgb: 20, 184, 166;             /* For transparency */

  /* Accent Colors - Teal Variants */
  --color-accent: rgba(94, 234, 212, 1);         /* #5eead4 */
  --color-accent-hover: rgba(45, 212, 191, 1);   /* #2dd4bf */
}
```

### Surface & Background Colors

```css
:root {
  /* Light Mode Surfaces */
  --color-background: rgba(252, 252, 249, 1);    /* #fcfcf9 */
  --color-surface: rgba(255, 255, 253, 1);       /* #fffffd */
  --color-surface-rgb: 255, 255, 253;            /* For transparency */
  
  /* Borders */
  --color-border: rgba(94, 82, 64, 0.2);         /* #5e5240 at 20% */
  --color-border-secondary: rgba(119, 124, 124, 0.2); /* Dark mode */
  --color-card-border: rgba(94, 82, 64, 0.12);   /* Subtle card borders */
}
```

### Text Color Hierarchy

```css
:root {
  /* Text Colors */
  --color-text: rgba(19, 52, 59, 1);             /* #13343b */
  --color-text-secondary: rgba(98, 108, 113, 1); /* #626c71 */
  --color-text-tertiary: rgba(98, 108, 113, 0.7); /* Muted text */
  --color-btn-primary-text: rgba(252, 252, 249, 1); /* Button text */
}
```

### Status Colors

```css
:root {
  /* Status Palette */
  --color-success: rgba(33, 128, 141, 1);        /* #21808d */
  --color-success-rgb: 33, 128, 141;
  --color-error: rgba(192, 21, 47, 1);           /* #c0152f */
  --color-error-rgb: 192, 21, 47;
  --color-warning: rgba(168, 75, 47, 1);         /* #a84b2f */
  --color-warning-rgb: 168, 75, 47;
  --color-info: rgba(98, 108, 113, 1);           /* #626c71 */
  --color-info-rgb: 98, 108, 113;
}
```

### Hero Background Gradient

**Primary gradient used across authentication and hero sections:**

```css
.auth-page__background {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

/* With accent overlay */
.auth-page__background::before {
  background: 
    radial-gradient(circle at 30% 40%, rgba(50, 184, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(50, 184, 198, 0.1) 0%, transparent 50%);
}
```

### Dark Mode Colors

```css
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);      /* #1f2121 */
    --color-surface: rgba(38, 40, 40, 1);         /* #262828 */
    --color-text: rgba(245, 245, 245, 1);         /* #f5f5f5 */
    --color-text-secondary: rgba(167, 169, 169, 0.7); /* #a7a9a9 */
    --color-primary: rgba(50, 184, 198, 1);       /* #32b8c6 */
    --color-border: rgba(119, 124, 124, 0.3);     /* #777c7c */
  }
}
```

### Color Usage Guidelines

#### Primary Colors
- **--color-primary**: CTAs, links, focus states, brand elements
- **--color-primary-hover**: Hover states for primary elements
- **Usage**: Maximum 1-2 primary elements per screen for hierarchy

#### Text Colors
- **--color-text**: Headings, primary content, important information
- **--color-text-secondary**: Body text, descriptions, secondary information
- **--color-text-tertiary**: Placeholder text, disabled states, subtle hints

---

## Typography System

### Font Family Stack

```css
:root {
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
}
```

### Font Size Scale

```css
:root {
  /* Typography Scale */
  --font-size-xs: 11px;     /* Small labels, captions */
  --font-size-sm: 12px;     /* Form labels, helper text */
  --font-size-base: 14px;   /* Body text, buttons */
  --font-size-md: 14px;     /* Alias for base */
  --font-size-lg: 16px;     /* Large body text, input text */
  --font-size-xl: 18px;     /* Subheadings, large buttons */
  --font-size-2xl: 20px;    /* Card titles, form titles */
  --font-size-3xl: 24px;    /* Section headings */
  --font-size-4xl: 30px;    /* Page titles, hero text */
}
```

### Font Weight Hierarchy

```css
:root {
  /* Font Weights */
  --font-weight-normal: 400;    /* Body text */
  --font-weight-medium: 500;    /* Labels, secondary headings */
  --font-weight-semibold: 550;  /* Buttons, important text */
  --font-weight-bold: 600;      /* Headings, titles */
}
```

### Line Height Standards

```css
:root {
  /* Line Heights */
  --line-height-tight: 1.2;     /* Headings, titles */
  --line-height-normal: 1.5;    /* Body text, forms */
  --letter-spacing-tight: -0.01em; /* Headings */
}
```

### Typography Implementation Examples

#### Headings

```css
/* H1 - Hero Titles */
.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

/* H2 - Section Headings */
.auth-page__form-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

/* H3 - Card Titles */
.auth-page__feature-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}
```

#### Body Text

```css
/* Primary Body Text */
.hero__subtitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

/* Secondary Text */
.auth-page__form-subtitle {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

/* Helper Text */
.auth-page__field-help {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}
```

#### Gradient Text Effects

```css
/* Brand Name Gradient */
.auth-page__brand-name {
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Highlight Text */
.auth-page__welcome-highlight {
  background: linear-gradient(135deg, #32b8c6 0%, #21808d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
```

---

## Spacing & Layout System

### Spacing Scale

```css
:root {
  /* Spacing Scale (4px base grid) */
  --space-0: 0;
  --space-1: 1px;      /* Borders, fine adjustments */
  --space-2: 2px;      /* Small gaps */
  --space-4: 4px;      /* Tight spacing */
  --space-6: 6px;      /* Small padding */
  --space-8: 8px;      /* Standard small spacing */
  --space-10: 10px;    /* Button padding */
  --space-12: 12px;    /* Medium spacing */
  --space-16: 16px;    /* Standard spacing unit */
  --space-20: 20px;    /* Large spacing */
  --space-24: 24px;    /* Section spacing */
  --space-32: 32px;    /* Large section spacing */
}
```

### Container System

```css
:root {
  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-max: 1400px; /* Auth pages */
}

/* Container Implementation */
.container {
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: 0 var(--space-16);
}

.auth-page__container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: var(--space-24);
}
```

### Grid Systems

#### Authentication Page Grid

```css
.auth-page__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-64);
  align-items: center;
  min-height: 80vh;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Responsive Grid */
@media (max-width: 1024px) {
  .auth-page__content {
    grid-template-columns: 1fr;
    gap: var(--space-48);
    text-align: center;
  }
}
```

#### Form Field Groups

```css
.auth-page__field-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
}

@media (max-width: 768px) {
  .auth-page__field-group {
    grid-template-columns: 1fr;
  }
}
```

#### Stats Grid

```css
.auth-page__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-16);
}

@media (max-width: 768px) {
  .auth-page__stats {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
}
```

### Responsive Breakpoints

```css
/* Mobile First Breakpoints */
/* Base: 320px+ (mobile) */

@media (max-width: 768px) {
  /* Mobile optimizations */
  .navbar__menu { display: none; }
  .auth-page__container { padding: var(--space-16); }
}

@media (max-width: 1024px) {
  /* Tablet adjustments */
  .auth-page__content { grid-template-columns: 1fr; }
}

@media (min-width: 1025px) {
  /* Desktop enhancements */
  .auth-page__content { grid-template-columns: 1fr 1fr; }
}
```

---

## Component Library

### Button System

#### Primary Button

**HTML Implementation:**
```html
<button class="btn btn--primary">Get Started</button>
<button class="btn btn--primary btn--lg">Start Free Trial</button>
<a href="/signup" class="btn btn--primary">Sign Up</a>
```

**CSS Implementation:**
```css
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: var(--space-1) solid transparent;
  border-radius: var(--radius-md);
  padding: var(--space-10) var(--space-16);
  cursor: pointer;
  transition: all 200ms ease;
  white-space: nowrap;
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
}

.btn--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(var(--color-primary-rgb), 0.2);
}

.btn--lg {
  padding: var(--space-12) var(--space-20);
  font-size: var(--font-size-lg);
}
```

#### Outline Button

**HTML Implementation:**
```html
<button class="btn btn--outline">Learn More</button>
<button class="btn btn--outline btn--lg">Watch Demo</button>
```

**CSS Implementation:**
```css
.btn--outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.2);
}
```

#### Secondary Button

**HTML Implementation:**
```html
<button class="btn btn--secondary">Cancel</button>
```

**CSS Implementation:**
```css
.btn--secondary {
  background: var(--color-surface);
  color: var(--color-text);
  border-color: var(--color-border);
}

.btn--secondary:hover {
  background: var(--color-background);
  border-color: var(--color-text-secondary);
}
```

### Form Components

#### Input Field with Icon

**HTML Implementation:**
```html
<div class="auth-page__field">
  <label class="auth-page__label" for="email">Email address</label>
  <div class="auth-page__input-wrapper">
    <div class="auth-page__input-icon">
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
      </svg>
    </div>
    <input type="email" id="email" class="auth-page__input"
           placeholder="<EMAIL>" required>
  </div>
</div>
```

**CSS Implementation:**
```css
.auth-page__field {
  margin-bottom: var(--space-20);
}

.auth-page__label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-6);
}

.auth-page__input-wrapper {
  position: relative;
}

.auth-page__input-icon {
  position: absolute;
  left: var(--space-16);
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: var(--color-text-secondary);
  pointer-events: none;
}

.auth-page__input {
  width: 100%;
  padding: var(--space-16) var(--space-20) var(--space-16) var(--space-48);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.auth-page__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow:
    0 0 0 3px rgba(var(--color-primary-rgb), 0.1),
    0 1px 2px rgba(0, 0, 0, 0.05);
  background: #ffffff;
}

.auth-page__input--no-icon {
  padding-left: var(--space-20);
}
```

#### Checkbox Component

**HTML Implementation:**
```html
<div class="auth-page__checkbox">
  <input type="checkbox" id="remember" class="auth-page__checkbox-input">
  <label for="remember" class="auth-page__checkbox-label">Remember me</label>
</div>
```

**CSS Implementation:**
```css
.auth-page__checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.auth-page__checkbox-input {
  width: 16px;
  height: 16px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
  cursor: pointer;
}

.auth-page__checkbox-input:checked {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.auth-page__checkbox-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  cursor: pointer;
}
```

### Card Components

#### Glass Morphism Form Card

**HTML Implementation:**
```html
<div class="auth-page__form-card">
  <div class="auth-page__form-header">
    <h3 class="auth-page__form-title">Sign in to your account</h3>
    <p class="auth-page__form-subtitle">
      Or <a href="/signup" class="auth-page__form-link">start your free trial</a>
    </p>
  </div>
  <!-- Form content -->
</div>
```

**CSS Implementation:**
```css
.auth-page__form-card {
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(var(--color-border-rgb), 0.3);
  border-radius: var(--radius-2xl);
  padding: var(--space-32);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
  position: relative;
  overflow: hidden;
}

.auth-page__form-card--centered {
  max-width: 480px;
  margin: 0 auto;
}

.auth-page__form-header {
  text-align: center;
  margin-bottom: var(--space-32);
}

.auth-page__form-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.auth-page__form-subtitle {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.auth-page__form-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: color 150ms ease;
}

.auth-page__form-link:hover {
  color: var(--color-primary-hover);
}
```

#### Feature Card

**HTML Implementation:**
```html
<div class="auth-page__feature">
  <div class="auth-page__feature-icon auth-page__feature-icon--success">
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
    </svg>
  </div>
  <div class="auth-page__feature-content">
    <div class="auth-page__feature-title">Real-time Analytics</div>
    <div class="auth-page__feature-description">Live dashboards updated every minute</div>
  </div>
</div>
```

**CSS Implementation:**
```css
.auth-page__feature {
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
  margin-bottom: var(--space-20);
}

.auth-page__feature-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.auth-page__feature-icon--success {
  background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.15), rgba(var(--color-success-rgb), 0.08));
  border: 2px solid rgba(var(--color-success-rgb), 0.2);
}

.auth-page__feature-icon--primary {
  background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.15), rgba(var(--color-primary-rgb), 0.08));
  border: 2px solid rgba(var(--color-primary-rgb), 0.2);
}

.auth-page__feature-icon svg {
  width: 20px;
  height: 20px;
  color: var(--color-success);
}

.auth-page__feature-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.auth-page__feature-description {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}
```

### Navigation Components

#### Fixed Navbar with Glass Morphism

**HTML Implementation:**
```html
<nav class="navbar" data-controller="landing-navbar">
  <div class="container">
    <div class="navbar__content">
      <!-- Logo and Brand -->
      <a href="/" class="navbar__brand">
        <div class="navbar__logo">
          <svg class="navbar__logo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="navbar__title">Data Reflow</span>
      </a>

      <!-- Desktop Navigation -->
      <div class="navbar__menu">
        <a href="#features" class="navbar__link">Features</a>
        <a href="#pricing" class="navbar__link">Pricing</a>
        <a href="/about" class="navbar__link">About</a>

        <div class="navbar__actions">
          <a href="/login" class="navbar__link">Sign In</a>
          <a href="/signup" class="btn btn--primary">Start Free Trial</a>
        </div>
      </div>

      <!-- Mobile Menu Button -->
      <button class="navbar__toggle" data-action="click->landing-navbar#toggleMobile">
        <span class="navbar__toggle-bar"></span>
        <span class="navbar__toggle-bar"></span>
        <span class="navbar__toggle-bar"></span>
      </button>
    </div>
  </div>
</nav>
```

**CSS Implementation:**
```css
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(var(--color-border-rgb), 0.3);
  padding: var(--space-12) 0;
  transition: all 200ms ease;
}

.navbar__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar__brand {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  text-decoration: none;
  color: var(--color-text);
}

.navbar__logo {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar__logo-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.navbar__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

.navbar__menu {
  display: flex;
  align-items: center;
  gap: var(--space-24);
}

.navbar__link {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-md);
  transition: all 150ms ease;
}

.navbar__link:hover {
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

/* Mobile Menu Toggle */
.navbar__toggle {
  display: none;
  flex-direction: column;
  gap: var(--space-4);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-8);
}

.navbar__toggle-bar {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  border-radius: var(--radius-sm);
  transition: all 200ms ease;
}

@media (max-width: 768px) {
  .navbar__menu {
    display: none;
  }

  .navbar__toggle {
    display: flex;
  }
}
```

#### Mobile Navigation

**HTML Implementation:**
```html
<div class="navbar__mobile-menu" data-landing-navbar-target="mobileMenu">
  <div class="navbar__mobile-content">
    <a href="#features" class="navbar__mobile-link">Features</a>
    <a href="#pricing" class="navbar__mobile-link">Pricing</a>
    <a href="/about" class="navbar__mobile-link">About</a>

    <div class="navbar__mobile-actions">
      <a href="/login" class="navbar__mobile-link">Sign In</a>
      <a href="/signup" class="btn btn--primary btn--lg">Start Free Trial</a>
    </div>
  </div>
</div>
```

**CSS Implementation:**
```css
.navbar__mobile-menu {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 250ms ease;
  z-index: 40;
}

.navbar__mobile-menu.is-open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.navbar__mobile-content {
  padding: var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.navbar__mobile-link {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-decoration: none;
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--color-border);
}

.navbar__mobile-actions {
  margin-top: var(--space-16);
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}
```

### Icon System

#### Icon Sizing Standards

```css
:root {
  /* Icon Sizes */
  --icon-xs: 12px;
  --icon-sm: 16px;
  --icon-md: 20px;
  --icon-lg: 24px;
  --icon-xl: 32px;
  --icon-2xl: 40px;
}

/* Icon Classes */
.icon {
  width: var(--icon-md);
  height: var(--icon-md);
  flex-shrink: 0;
}

.icon--xs { width: var(--icon-xs); height: var(--icon-xs); }
.icon--sm { width: var(--icon-sm); height: var(--icon-sm); }
.icon--lg { width: var(--icon-lg); height: var(--icon-lg); }
.icon--xl { width: var(--icon-xl); height: var(--icon-xl); }
```

#### Icon Usage Examples

**Input Icons:**
```html
<div class="auth-page__input-icon">
  <svg class="icon icon--sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
  </svg>
</div>
```

**Feature Icons:**
```html
<div class="auth-page__feature-icon auth-page__feature-icon--success">
  <svg class="icon icon--md" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
  </svg>
</div>
```

---

## Visual Effects & Interactions

### Shadow System

```css
:root {
  /* Shadow Scale */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* Glass Morphism Shadow */
  --shadow-glass:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
}
```

### Border Radius System

```css
:root {
  /* Border Radius Scale */
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-full: 9999px;
}
```

### Animation & Timing

```css
:root {
  /* Animation Duration */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;

  /* Easing Functions */
  --ease-linear: linear;
  --ease-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### Glass Morphism Implementation

#### Standard Glass Effect

```css
.glass-effect {
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(var(--color-border-rgb), 0.3);
  box-shadow: var(--shadow-glass);
  border-radius: var(--radius-2xl);
}

/* Dark Background Variant */
.glass-effect--dark {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
}
```

#### Authentication Page Background

```css
.auth-page__background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  z-index: -2;
}

.auth-page__background::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 30% 40%, rgba(50, 184, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(50, 184, 198, 0.1) 0%, transparent 50%);
  z-index: -1;
}
```

### Hover & Focus States

#### Standard Hover Pattern

```css
.interactive-element {
  transition: all var(--duration-normal) var(--ease-standard);
}

.interactive-element:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.interactive-element:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}
```

#### Focus States

```css
.focusable:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.4);
  border-color: var(--color-primary);
}

.focusable:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
```

---

## Implementation Guidelines

### Do's and Don'ts

#### ✅ Do's

**Use CSS Custom Properties**
```css
/* ✅ Correct */
.my-component {
  color: var(--color-text);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
}
```

**Follow Naming Conventions**
```css
/* ✅ Correct - BEM methodology */
.auth-page__form-card { }
.auth-page__form-card--centered { }
.auth-page__input--error { }
```

**Use Established Button Classes**
```html
<!-- ✅ Correct -->
<button class="btn btn--primary btn--lg">Submit</button>
<a href="/signup" class="btn btn--outline">Learn More</a>
```

**Implement Proper Focus States**
```css
/* ✅ Correct */
.auth-page__input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}
```

**Use Semantic HTML**
```html
<!-- ✅ Correct -->
<nav class="navbar">
  <ul class="navbar__menu">
    <li><a href="/features" class="navbar__link">Features</a></li>
  </ul>
</nav>
```

#### ❌ Don'ts

**Don't Use Hardcoded Values**
```css
/* ❌ Incorrect */
.my-component {
  color: #21808d;
  padding: 16px;
  border-radius: 8px;
}

/* ✅ Correct */
.my-component {
  color: var(--color-primary);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
}
```

**Don't Create Custom Button Styles**
```css
/* ❌ Incorrect */
.my-custom-button {
  background: linear-gradient(45deg, blue, purple);
  padding: 15px 25px;
  border-radius: 20px;
}

/* ✅ Correct */
.btn.btn--primary {
  /* Extend existing button if needed */
}
```

**Don't Ignore Responsive Design**
```css
/* ❌ Incorrect */
.my-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

/* ✅ Correct */
.my-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-16);
}
```

### Copy-Paste Code Examples

#### Complete Authentication Form

```html
<div class="auth-page">
  <div class="auth-page__background"></div>

  <div class="auth-page__container">
    <div class="auth-page__content">
      <div class="auth-page__form-container">
        <div class="auth-page__form-card">
          <div class="auth-page__form-header">
            <h3 class="auth-page__form-title">Sign in to your account</h3>
            <p class="auth-page__form-subtitle">
              Or <a href="/signup" class="auth-page__form-link">start your free trial</a>
            </p>
          </div>

          <form class="auth-page__form">
            <div class="auth-page__field">
              <label class="auth-page__label" for="email">Email address</label>
              <div class="auth-page__input-wrapper">
                <div class="auth-page__input-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                  </svg>
                </div>
                <input type="email" id="email" class="auth-page__input"
                       placeholder="<EMAIL>" required>
              </div>
            </div>

            <div class="auth-page__field">
              <label class="auth-page__label" for="password">Password</label>
              <div class="auth-page__input-wrapper">
                <div class="auth-page__input-icon">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                  </svg>
                </div>
                <input type="password" id="password" class="auth-page__input"
                       placeholder="Enter your password" required>
              </div>
            </div>

            <div class="auth-page__submit">
              <button type="submit" class="btn btn--primary btn--lg auth-page__submit-btn">
                Sign in
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### Complete Feature Card

```html
<div class="auth-page__feature">
  <div class="auth-page__feature-icon auth-page__feature-icon--success">
    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
    </svg>
  </div>
  <div class="auth-page__feature-content">
    <div class="auth-page__feature-title">Real-time Analytics</div>
    <div class="auth-page__feature-description">
      Live dashboards updated every minute with real-time data processing
    </div>
  </div>
</div>
```

### Extending Components

#### Adding New Button Variants

```css
/* Extend existing button system */
.btn--success {
  background: var(--color-success);
  color: var(--color-btn-primary-text);
  border-color: var(--color-success);
}

.btn--success:hover {
  background: var(--color-success-hover);
  border-color: var(--color-success-hover);
}
```

#### Creating New Form Components

```css
/* Follow established patterns */
.auth-page__textarea {
  width: 100%;
  padding: var(--space-16) var(--space-20);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
  resize: vertical;
  min-height: 120px;
}

.auth-page__textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}
```

### Troubleshooting Guide

#### Common Issues

**Issue: Glass morphism not working**
```css
/* ❌ Missing backdrop-filter support */
.glass-card {
  background: rgba(255, 255, 255, 0.8);
}

/* ✅ Add fallback and proper implementation */
.glass-card {
  background: rgba(var(--color-surface-rgb), 0.95);
  backdrop-filter: blur(20px);
  /* Fallback for browsers without backdrop-filter */
  @supports not (backdrop-filter: blur(20px)) {
    background: var(--color-surface);
  }
}
```

**Issue: Focus states not visible**
```css
/* ❌ Removing outline without replacement */
.my-input:focus {
  outline: none;
}

/* ✅ Provide alternative focus indicator */
.my-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}
```

**Issue: Mobile layout breaking**
```css
/* ❌ Fixed grid columns */
.my-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

/* ✅ Responsive grid */
.my-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-16);
}

@media (max-width: 768px) {
  .my-grid {
    grid-template-columns: 1fr;
  }
}
```

### Performance Considerations

#### Efficient CSS

```css
/* ✅ Use CSS custom properties for consistency */
:root {
  --component-padding: var(--space-16);
  --component-border: 1px solid var(--color-border);
}

/* ✅ Minimize repaints with transform */
.hover-element:hover {
  transform: translateY(-2px);
  /* Instead of changing top/bottom */
}

/* ✅ Use will-change for animated elements */
.animated-element {
  will-change: transform;
  transition: transform var(--duration-normal) var(--ease-standard);
}
```

#### Accessibility Checklist

- [ ] Color contrast ratio ≥ 4.5:1 for normal text
- [ ] Color contrast ratio ≥ 3:1 for large text
- [ ] Focus indicators visible and consistent
- [ ] Keyboard navigation works for all interactive elements
- [ ] Semantic HTML structure with proper headings
- [ ] Alt text for images and icons
- [ ] Form labels properly associated with inputs
- [ ] Touch targets ≥ 44px on mobile devices

---

## Conclusion

This design system guide provides the foundation for maintaining consistency and quality across the Data Reflow platform. By following these guidelines and using the established patterns, we ensure a cohesive, professional, and accessible user experience that builds trust with our enterprise customers.

**Key Takeaways:**
- Always use CSS custom properties instead of hardcoded values
- Follow established component patterns and naming conventions
- Implement proper accessibility standards
- Test responsive behavior across all devices
- Maintain visual hierarchy and professional aesthetics

For questions or suggestions about extending this design system, please refer to the development team or create an issue in the project repository.
```
```
```
